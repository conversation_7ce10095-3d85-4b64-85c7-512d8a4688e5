package com.mh.web.controller;

import com.alibaba.fastjson.JSONArray;
import com.mh.common.core.domain.R;
import com.mh.common.dingding.service.DingdingNoticeService;
import com.mh.common.dingding.domain.dto.DingRobotFileMessageRequest;
import com.mh.common.dingding.domain.dto.DingWorkMessageCardRequest;
import com.mh.common.dingding.domain.dto.DingWorkMessageRequest;
import io.swagger.v3.oas.annotations.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 钉钉通知控制器
 *
 * <AUTHOR>
 * @date 2021/12/24
 */
@RestController
@RequestMapping("/dingding/notice")
@Tag(name = "钉钉通知", description = "钉钉通知相关接口")
@RequiredArgsConstructor
@Validated
public class DingdingNoticeController {
    private final DingdingNoticeService dingdingNoticeService;

    /**
     * 获取钉钉访问令牌
     */
    @Operation(summary = "获取钉钉访问令牌")
    @GetMapping("/token")
    public R<String> getDingToken() throws Exception {
        String token = dingdingNoticeService.getDingToken();
        return R.ok("获取访问令牌成功", token);
    }

    /**
     * 根据手机号获取钉钉用户ID
     */
    @Operation(summary = "根据手机号获取钉钉用户ID")
    @GetMapping("/userid")
    public R<String> getDingUserID(@Parameter(description = "手机号") @RequestParam("mobile") String mobile) {
        String userId = dingdingNoticeService.getDingUserID(mobile);
        return R.ok("获取用户ID成功", userId);
    }

    /**
     * 获取部门ID列表
     */
    @Operation(summary = "获取部门ID列表")
    @GetMapping("/dept/ids")
    public R<List<Long>> getDeptIdList(@Parameter(description = "部门ID") @RequestParam("deptId") Long deptId) {
        List<Long> deptIdList = dingdingNoticeService.getDeptIdList(deptId);
        return R.ok("获取部门ID列表成功", deptIdList);
    }

    /**
     * 获取部门列表
     */
    @Operation(summary = "获取部门列表")
    @GetMapping("/dept/list")
    public R<JSONArray> getDeptList(@Parameter(description = "部门ID") @RequestParam("deptId") Long deptId) {
        JSONArray deptList = dingdingNoticeService.getDeptList(deptId);
        return R.ok("获取部门列表成功", deptList);
    }

    /**
     * 获取部门用户列表
     */
    @Operation(summary = "获取部门用户列表")
    @GetMapping("/users")
    public R<JSONArray> getUserList(@Parameter(description = "部门ID") @RequestParam("deptId") Long deptId) {
        JSONArray userList = dingdingNoticeService.getUserList(deptId);
        return R.ok("获取部门用户列表成功", userList);
    }

    /**
     * 发送钉钉工作消息-文本消息
     */
    @Operation(summary = "发送钉钉工作消息-文本消息")
    @PostMapping("/work/text")
    public R<Void> sendDingWorkMessage(@Valid @RequestBody DingWorkMessageRequest request) throws Exception {
        boolean success = dingdingNoticeService.sendDingWorkMessage(request.getText(), request.getUserIds());
        return success ? R.ok("消息发送成功") : R.fail("消息发送失败");
    }

    /**
     * 发送钉钉工作消息-卡片消息
     */
    @Operation(summary = "发送钉钉工作消息-卡片消息")
    @PostMapping("/work/card")
    public R<Void> sendDingWorkMessageCard(@Valid @RequestBody DingWorkMessageCardRequest request) throws Exception {
        boolean success = dingdingNoticeService.sendDingWorkMessageCard(
                request.getTitle(), request.getText(), request.getUrl(), request.getUserIds());
        return success ? R.ok("卡片消息发送成功") : R.fail("卡片消息发送失败");
    }

    /**
     * 发送机器人文本消息
     */
    @Operation(summary = "发送机器人文本消息")
    @PostMapping("/robot/text")
    public R<Void> sendRobotTextMessage(@Valid @RequestBody DingWorkMessageRequest request) {
        boolean success = dingdingNoticeService.sendRobotTextMessage(request.getText(), request.getUserIds());
        return success ? R.ok("机器人文本消息发送成功") : R.fail("机器人文本消息发送失败");
    }

    /**
     * 发送机器人卡片消息
     */
    @Operation(summary = "发送机器人卡片消息")
    @PostMapping("/robot/card")
    public R<Void> sendRobotCardMessage(@Valid @RequestBody DingWorkMessageCardRequest request) {
        boolean success = dingdingNoticeService.sendRobotCardMessage(
                request.getTitle(), request.getText(), request.getUrl(), request.getUserIds());
        return success ? R.ok("机器人卡片消息发送成功") : R.fail("机器人卡片消息发送失败");
    }

    /**
     * 上传文件到钉钉
     */
    @Operation(summary = "上传文件到钉钉")
    @PostMapping("/upload")
    public R<String> uploadFile(
            @Parameter(description = "文件路径") @RequestParam("filePath") String filePath,
            @Parameter(description = "访问令牌") @RequestParam("accessToken") String accessToken) {
        String mediaId = dingdingNoticeService.uploadFile(filePath, accessToken);
        if (mediaId != null) {
            return R.ok("文件上传成功", mediaId);
        } else {
            return R.fail("文件上传失败");
        }
    }

    /**
     * 发送机器人文件消息
     */
    @Operation(summary = "发送机器人文件消息")
    @PostMapping("/robot/file")
    public R<Void> sendRobotFileMessage(@Valid @RequestBody DingRobotFileMessageRequest request) {
        boolean success = dingdingNoticeService.sendRobotFileMessage(
                request.getUserIds(), request.getFilePath(), request.getFileName());
        return success ? R.ok("机器人文件消息发送成功") : R.fail("机器人文件消息发送失败");
    }

}
