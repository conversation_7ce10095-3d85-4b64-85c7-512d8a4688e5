<?xml version="1.0" encoding="UTF-8"?>
<ViewConfig>
  <Arguments/>
  <Context/>
  <Model>
    <DataType name="dtCondition">
      <PropertyDef name="ddh">
        <Property name="label">订单号</Property>
      </PropertyDef>
      <PropertyDef name="p4">
        <Property name="label">SAP料号</Property>
      </PropertyDef>
      <PropertyDef name="ORDER_DATE_B">
        <Property name="label">下单日期</Property>
        <Property name="dataType">Date</Property>
      </PropertyDef>
      <PropertyDef name="ORDER_DATE_E">
        <Property name="label">至</Property>
        <Property name="dataType">Date</Property>
      </PropertyDef>
      <PropertyDef name="JH_DATE_B">
        <Property name="label">回复交期</Property>
        <Property name="dataType">Date</Property>
      </PropertyDef>
      <PropertyDef name="JH_DATE_E">
        <Property name="label">至</Property>
        <Property name="dataType">Date</Property>
      </PropertyDef>
      <PropertyDef name="p1">
        <Property name="label">售达方</Property>
        <Property name="mapping">
          <Property name="keyProperty">enumvCode</Property>
          <Property name="valueProperty">enumvName</Property>
          <Property name="mapValues">${dorado.getDataProvider(&quot;enumController#queryEnumv&quot;).getResult(&quot;ORDER_TYPE&quot;)}</Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="p8">
        <Property name="label">玖维生产料号</Property>
      </PropertyDef>
      <PropertyDef name="createdby">
        <Property name="label">创建人</Property>
      </PropertyDef>
      <PropertyDef name="p2">
        <Property name="label">送达方</Property>
      </PropertyDef>
      <PropertyDef name="whetherMS">
        <Property name="label">TC&amp;MS</Property>
        <Property name="defaultValue">不包含</Property>
      </PropertyDef>
      <PropertyDef name="fhd">
        <Property name="label">发货地</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="ZJ">ZJ</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="status">
        <Property></Property>
        <Property name="label">状态</Property>
        <Property name="dataType">String</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="3">已评审</Property>
              <Property name="2">已点库</Property>
              <Property name="1">待评审</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="requireDateFrom">
        <Property name="dataType">Date</Property>
        <Property name="label">要求交期</Property>
      </PropertyDef>
      <PropertyDef name="requireDateTo">
        <Property name="dataType">Date</Property>
        <Property name="label">至</Property>
      </PropertyDef>
      <PropertyDef name="stockStatus">
        <Property name="label">库存状态</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="1">成品库存</Property>
              <Property name="2">BB库存</Property>
              <Property name="3">待生产</Property>
            </Entity>
          </Property>
        </Property>
        <Property name="dataType">int</Property>
      </PropertyDef>
    </DataType>
    <DataType name="dtSearch">
      <PropertyDef name="pkId">
        <Property></Property>
        <Property name="label">pk_id</Property>
      </PropertyDef>
      <PropertyDef name="custPurchaseNo">
        <Property></Property>
        <Property name="label">客户采购订单号</Property>
      </PropertyDef>
      <PropertyDef name="custPurchaseItem">
        <Property></Property>
        <Property name="label">客户采购订单项目</Property>
      </PropertyDef>
      <PropertyDef name="sellTo">
        <Property></Property>
        <Property name="label">售达方</Property>
      </PropertyDef>
      <PropertyDef name="shipTo">
        <Property></Property>
        <Property name="label">送达方</Property>
      </PropertyDef>
      <PropertyDef name="customerType">
        <Property></Property>
        <Property name="label">客户类型</Property>
      </PropertyDef>
      <PropertyDef name="customerMaterial1">
        <Property></Property>
        <Property name="label">客户料号</Property>
      </PropertyDef>
      <PropertyDef name="qty">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">下单数量(KPCS)</Property>
      </PropertyDef>
      <PropertyDef name="orderType">
        <Property></Property>
        <Property name="label">订单类型</Property>
      </PropertyDef>
      <PropertyDef name="headRemark">
        <Property></Property>
        <Property name="label">抬头备注</Property>
      </PropertyDef>
      <PropertyDef name="itemRemark">
        <Property></Property>
        <Property name="label">行项目备注</Property>
      </PropertyDef>
      <PropertyDef name="orderNo">
        <Property></Property>
        <Property name="label">订单号</Property>
      </PropertyDef>
      <PropertyDef name="status">
        <Property></Property>
        <Property name="label">状态</Property>
        <Property name="dataType">int</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="1">待评审</Property>
              <Property name="2">已点库</Property>
              <Property name="3">已评审</Property>
              <Property name="4">已下计划</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="stockStatus">
        <Property name="dataType">int</Property>
        <Property name="label">库存状态</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="0"> </Property>
              <Property name="1">成品库存</Property>
              <Property name="2">BB库存</Property>
              <Property name="3">待生产</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="creater">
        <Property></Property>
        <Property name="label">创建人</Property>
      </PropertyDef>
      <PropertyDef name="requireDate">
        <Property name="dataType">Date</Property>
        <Property name="label">要求交期</Property>
      </PropertyDef>
      <PropertyDef name="autoReplyDate">
        <Property name="dataType">Date</Property>
        <Property name="label">自动回复交期</Property>
      </PropertyDef>

      <PropertyDef name="dispatchType">
        <Property name="dataType">int</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="1">成品调料</Property>
              <Property name="2">bb调料</Property>
              <Property name="3">镀前bb调料</Property>
              <Property name="0">未知</Property>
            </Entity>
          </Property>
        </Property>
        <Property name="label">调料类型</Property>
      </PropertyDef>
      <PropertyDef name="dispatchReplyDate">
        <Property name="dataType">Date</Property>
        <Property name="label">调料回复交期</Property>
      </PropertyDef>
      <PropertyDef name="replyDateFirst">
        <Property name="dataType">Date</Property>
        <Property name="label">首次回复交期</Property>
      </PropertyDef>
      <PropertyDef name="sapNo">
        <Property></Property>
        <Property name="label">sap料号</Property>
      </PropertyDef>
      <PropertyDef name="beskz">
        <Property name="label">物料采购类型</Property>
        <Property name="dataType">String</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="E">自制</Property>
              <Property name="F">外采</Property>
              <Property name="X">混合</Property>
              <Property name="D">定制</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="price">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">单价</Property>
      </PropertyDef>
      <PropertyDef name="amount">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">金额</Property>
      </PropertyDef>
      <PropertyDef name="currency">
        <Property></Property>
        <Property name="label">货币</Property>
      </PropertyDef>
      <PropertyDef name="trStock">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">成品库存</Property>
      </PropertyDef>
      <PropertyDef name="bbStock">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">BB库存</Property>
      </PropertyDef>
      <PropertyDef name="trProduceQty">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">成品生产数量</Property>
      </PropertyDef>
      <PropertyDef name="waitBoxQty">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">待排箱数量</Property>
      </PropertyDef>
      <PropertyDef name="produceNo">
        <Property></Property>
        <Property name="label">玖维生产料号</Property>
      </PropertyDef>
      <PropertyDef name="bbBeforeQty">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">电镀前BB数量</Property>
      </PropertyDef>
      <PropertyDef name="bbAfterQty">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">电镀后BB数量</Property>
      </PropertyDef>
      <PropertyDef name="orderMark">
        <Property></Property>
        <Property name="label">订单标识</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="0">KSV</Property>
              <Property name="1">KEV</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="deliveryPlace">
        <Property></Property>
        <Property name="label">发货地</Property>
      </PropertyDef>
      <PropertyDef name="sync">
        <Property></Property>
        <Property name="label">同步状态</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="1">是</Property>
              <Property name="0">否</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="syncDate">
        <Property name="dataType">Date</Property>
        <Property name="label">同步时间</Property>
      </PropertyDef>
      <PropertyDef name="syncer">
        <Property></Property>
        <Property name="label">同步人</Property>
      </PropertyDef>
      <PropertyDef name="msg">
        <Property></Property>
        <Property name="label">同步信息</Property>
      </PropertyDef>
      <PropertyDef name="orderItem">
        <Property></Property>
        <Property name="label">订单项目</Property>
      </PropertyDef>
      <PropertyDef name="specifications">
        <Property></Property>
        <Property name="label">规格</Property>
      </PropertyDef>
      <PropertyDef name="accuracy">
        <Property></Property>
        <Property name="label">精度</Property>
      </PropertyDef>
      <PropertyDef name="power">
        <Property></Property>
        <Property name="label">功率</Property>
      </PropertyDef>
      <PropertyDef name="packType">
        <Property></Property>
        <Property name="label">包装方式</Property>
      </PropertyDef>
      <PropertyDef name="packQty">
        <Property></Property>
        <Property name="label">包装数量</Property>
      </PropertyDef>
      <PropertyDef name="resistanceNumber">
        <Property></Property>
        <Property name="label">阻值数值</Property>
      </PropertyDef>
      <PropertyDef name="resistance">
        <Property></Property>
        <Property name="label">阻值</Property>
      </PropertyDef>
      <PropertyDef name="standardNo">
        <Property></Property>
        <Property name="label">玖维标准料号</Property>
      </PropertyDef>
      <PropertyDef name="characteristic1">
        <Property></Property>
        <Property name="label">特性值1</Property>
      </PropertyDef>
      <PropertyDef name="characteristic2">
        <Property></Property>
        <Property name="label">特性值2</Property>
      </PropertyDef>
      <PropertyDef name="ppm">
        <Property></Property>
        <Property name="label">温度系数</Property>
      </PropertyDef>
      <PropertyDef name="returnReason">
        <Property></Property>
        <Property name="label">退回原因</Property>
      </PropertyDef>
      <PropertyDef name="spareMark">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">是否从备料单中分批</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="1">是</Property>
              <Property name="0">否</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="bzkc">
        <Property name="label">不足量</Property>
      </PropertyDef>
      <PropertyDef name="materialGroup">
        <Property></Property>
        <Property name="label">物料组</Property>
      </PropertyDef>
      <PropertyDef name="jtdm">
        <Property></Property>
        <Property name="label">集团代码</Property>
      </PropertyDef>
      <PropertyDef name="supplyType">
        <Property></Property>
        <Property name="label">计划类型</Property>
        <Property name="dataType">int</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="1">外调</Property>
              <Property name="0">自产</Property>
              <Property name="2">内部调料</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="zguige">
        <Property name="label">主规格</Property>
      </PropertyDef>
      <PropertyDef name="version">
        <Property name="dataType">int</Property>
        <Property name="label">订单主表version</Property>
      </PropertyDef>
      <PropertyDef name="resisValueRange">
        <Property name="label">阻值范围</Property>
      </PropertyDef>
      <PropertyDef name="tableRemark">
        <Property name="label">表标识</Property>
      </PropertyDef>
      <PropertyDef name="replyDate">
        <Property name="dataType">Date</Property>
        <Property name="label">回复交期</Property>
      </PropertyDef>
      <PropertyDef name="subTime">
        <Property name="dataType">DateTime</Property>
        <Property name="label">提交时间</Property>
      </PropertyDef>
      <PropertyDef name="createTime">
        <Property name="dataType">Date</Property>
        <Property name="label">创建时间</Property>
      </PropertyDef>
      <PropertyDef name="stockHouse">
        <Property name="label">库别</Property>
      </PropertyDef>
      <PropertyDef name="mrkb">
        <Property name="label">默认库别</Property>
      </PropertyDef>
      <PropertyDef name="sapOrderNo">
        <Property name="label">SAP销售订单号</Property>
      </PropertyDef>
      <PropertyDef name="ktsch">
        <Property name="label">制程</Property>
      </PropertyDef>
      <PropertyDef name="creditDeducted">
        <Property name="dataType">int</Property>
        <Property name="label">信用额度扣减</Property>
      </PropertyDef>
      <PropertyDef name="duedays">
        <Property name="label">预估入库日期</Property>
      </PropertyDef>
      <PropertyDef name="plannedCompletionDate">
        <Property name="label">计划完成日期</Property>
      </PropertyDef>
      <PropertyDef name="deliveryStatus">
        <Property name="dataType">int</Property>
        <Property name="label">交期状态</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="0">可提交审核</Property>
              <Property name="1">审核中</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="specialRemark">
        <Property name="label">特殊生产要求备注</Property>
      </PropertyDef>
    </DataType>
    <DataType name="dataType1">
      <PropertyDef name="replyDate">
        <Property name="dataType">Date</Property>
        <Property name="label">时间</Property>
      </PropertyDef>
      <PropertyDef name="cause">
        <Property name="dataType">String</Property>
        <Property name="label">原因</Property>
        <Property name="mapping">
          <Property name="keyProperty">enumvCode</Property>
          <Property name="valueProperty">enumvName</Property>
          <Property name="mapValues">${dorado.getDataProvider(&quot;enumController#queryEnumv&quot;).getResult(&quot;updatecause&quot;)}</Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="remark">
        <Property name="dataType">String</Property>
        <Property name="label">备注</Property>
      </PropertyDef>
    </DataType>
    <DataType name="dataTypeOrderSplit">
      <PropertyDef name="orderNo">
        <Property name="dataType">String</Property>
        <Property name="label">orderNo</Property>
      </PropertyDef>
      <PropertyDef name="pkId">
        <Property name="dataType">String</Property>
        <Property name="label">pkId</Property>
      </PropertyDef>
      <PropertyDef name="qty">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">拆分下单数量</Property>
      </PropertyDef>
    </DataType>
    <DataType name="dtBlSearch">
      <PropertyDef name="orderSpareNo">
        <Property name="label">备料单号</Property>
      </PropertyDef>
      <PropertyDef name="orderSpareItem">
        <Property></Property>
        <Property name="label">备料单行项目</Property>
      </PropertyDef>
      <PropertyDef name="createStart">
        <Property name="label">下单时间</Property>
        <Property name="dataType">Date</Property>
      </PropertyDef>
      <PropertyDef name="createEnd">
        <Property name="label">至</Property>
        <Property name="dataType">Date</Property>
      </PropertyDef>
      <PropertyDef name="producePn">
        <Property name="label">玖维生产料号</Property>
      </PropertyDef>
      <PropertyDef name="hfjqStart">
        <Property name="label">回复日期</Property>
        <Property name="dataType">Date</Property>
      </PropertyDef>
      <PropertyDef name="hfjqEnd">
        <Property name="label">至</Property>
        <Property name="dataType">Date</Property>
      </PropertyDef>
      <PropertyDef name="sellTo">
        <Property name="label">售达方</Property>
      </PropertyDef>
      <PropertyDef name="shipTo">
        <Property name="label">送达方</Property>
      </PropertyDef>
      <PropertyDef name="creater">
        <Property name="label">创建人</Property>
      </PropertyDef>
      <PropertyDef name="sapPn">
        <Property name="label">sap料号</Property>
      </PropertyDef>
      <PropertyDef name="deliveryFactory">
        <Property name="label">发货地</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="3">ZJ</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="stockStatus">
        <Property name="label">库存状态</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="1">成品库存</Property>
              <Property name="2">BB库存</Property>
              <Property name="3">待生产</Property>
            </Entity>
          </Property>
        </Property>
        <Property name="dataType">int</Property>
      </PropertyDef>
      <PropertyDef name="whetherMS">
        <Property name="label">TC&amp;MS</Property>
        <Property name="defaultValue">不包含</Property>
      </PropertyDef>
    </DataType>
    <DataType name="dtBl">
      <Property name="creationType">com.dtt.scm.order.spare.chip.entity.OrderSpareChipEntity</Property>
      <PropertyDef name="pkId">
        <Property></Property>
        <Property name="label">主键,uuid</Property>
        <Property name="visible">false</Property>
      </PropertyDef>
      <PropertyDef name="orderSpareNo">
        <Property></Property>
        <Property name="label">备料单号</Property>
      </PropertyDef>
      <PropertyDef name="orderSpareItem">
        <Property></Property>
        <Property name="label">备料单行项目</Property>
      </PropertyDef>
      <PropertyDef name="custPurchaseNo">
        <Property></Property>
        <Property name="label">客户采购订单号</Property>
      </PropertyDef>
      <PropertyDef name="custPurchaseItem">
        <Property></Property>
        <Property name="label">客户采购订单项目</Property>
      </PropertyDef>
      <PropertyDef name="sellTo">
        <Property></Property>
        <Property name="label">售达方</Property>
      </PropertyDef>
      <PropertyDef name="shipTo">
        <Property></Property>
        <Property name="label">送达方</Property>
      </PropertyDef>
      <PropertyDef name="customerPn">
        <Property></Property>
        <Property name="label">客户料号</Property>
      </PropertyDef>
      <PropertyDef name="sapPn">
        <Property></Property>
        <Property name="label">sap料号</Property>
      </PropertyDef>
      <PropertyDef name="producePn">
        <Property></Property>
        <Property name="label">生产料号</Property>
      </PropertyDef>
      <PropertyDef name="standardPn">
        <Property></Property>
        <Property name="label">标准料号</Property>
      </PropertyDef>
      <PropertyDef name="originNum">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">原始数量</Property>
      </PropertyDef>
      <PropertyDef name="leftNum">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">剩余数量</Property>
      </PropertyDef>
      <PropertyDef name="remark">
        <Property></Property>
        <Property name="label">备注</Property>
      </PropertyDef>
      <PropertyDef name="quantity">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">下单量</Property>
      </PropertyDef>
      <PropertyDef name="status">
        <Property></Property>
        <Property name="label">状态</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="1">待评审</Property>
              <Property name="2">点库完成</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="stockStatus">
        <Property name="dataType">int</Property>
        <Property name="label">库存状态</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="0"> </Property>
              <Property name="1">成品库存</Property>
              <Property name="2">BB库存</Property>
              <Property name="3">待生产</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="needCancelQty">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">需取消数量</Property>
      </PropertyDef>
      <PropertyDef name="cancelQuantity">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">已取消数量</Property>
      </PropertyDef>
      <PropertyDef name="trStock">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">成品库存量</Property>
      </PropertyDef>
      <PropertyDef name="specsName">
        <Property></Property>
        <Property name="label">品名规格</Property>
      </PropertyDef>
      <PropertyDef name="power">
        <Property></Property>
        <Property name="label">功率</Property>
      </PropertyDef>
      <PropertyDef name="accuracy">
        <Property></Property>
        <Property name="label">精度</Property>
      </PropertyDef>
      <PropertyDef name="resistance">
        <Property></Property>
        <Property name="label">阻值</Property>
      </PropertyDef>
      <PropertyDef name="resistanceValue">
        <Property></Property>
        <Property name="label">阻值数值</Property>
      </PropertyDef>
      <PropertyDef name="packQuantity">
        <Property></Property>
        <Property name="label">包装数量</Property>
      </PropertyDef>
      <PropertyDef name="packType">
        <Property></Property>
        <Property name="label">包装方式</Property>
      </PropertyDef>
      <PropertyDef name="deliveryFactory">
        <Property name="dataType">int</Property>
        <Property name="label">发货工厂</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="3">浙江</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="message">
        <Property></Property>
        <Property name="label">导入信息</Property>
      </PropertyDef>
      <PropertyDef name="submitTime">
        <Property name="dataType">Date</Property>
        <Property name="label">提交时间</Property>
      </PropertyDef>
      <PropertyDef name="requireDate">
        <Property></Property>
        <Property name="label">要求交期</Property>
      </PropertyDef>
      <PropertyDef name="replyDate">
        <Property></Property>
        <Property name="label">回复交期</Property>
      </PropertyDef>
      <PropertyDef name="remarkDate">
        <Property name="dataType">String</Property>
        <Property name="label">备注</Property>
      </PropertyDef>
      <PropertyDef name="hfjq">
        <Property name="dataType">Date</Property>
        <Property name="label">新回复交期</Property>
      </PropertyDef>
      <PropertyDef name="cause">
        <Property></Property>
        <Property name="label">原因</Property>
      </PropertyDef>
      <PropertyDef name="syncer">
        <Property></Property>
        <Property name="label">同步人</Property>
      </PropertyDef>
      <PropertyDef name="creater">
        <Property></Property>
        <Property name="label">创建人</Property>
      </PropertyDef>
      <PropertyDef name="createTime">
        <Property name="dataType">Date</Property>
        <Property name="label">创建时间</Property>
      </PropertyDef>
      <PropertyDef name="version">
        <Property name="dataType">short</Property>
        <Property name="label">版本号</Property>
        <Property name="visible">false</Property>
      </PropertyDef>
      <PropertyDef name="bbStock">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">BB库存量</Property>
      </PropertyDef>
      <PropertyDef name="trProduceNum">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">成品生产数量</Property>
      </PropertyDef>
      <PropertyDef name="bbProduceNum">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">bb生产数量</Property>
      </PropertyDef>
      <PropertyDef name="price">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">单价</Property>
      </PropertyDef>
      <PropertyDef name="currency">
        <Property></Property>
        <Property name="label">货币</Property>
      </PropertyDef>
      <PropertyDef name="amount">
        <Property name="dataType">BigDecimal</Property>
        <Property name="label">金额</Property>
      </PropertyDef>
      <PropertyDef name="stockHouse">
        <Property></Property>
        <Property name="label">库别</Property>
      </PropertyDef>
      <PropertyDef name="customerAbbre">
        <Property></Property>
        <Property name="label">客户简称</Property>
      </PropertyDef>
      <PropertyDef name="ktsch">
        <Property></Property>
      </PropertyDef>
      <PropertyDef name="creditDeducted">
        <Property name="dataType">Integer</Property>
      </PropertyDef>
      <PropertyDef name="deliveryStatus">
        <Property name="dataType">int</Property>
        <Property name="label">交期状态</Property>
        <Property name="mapping">
          <Property name="mapValues">
            <Entity>
              <Property name="0">可提交审核</Property>
              <Property name="1">审核中</Property>
            </Entity>
          </Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="duedays">
        <Property name="label">预估入库日期</Property>
      </PropertyDef>
      <PropertyDef name="plannedCompletionDate">
        <Property name="label">计划完成日期</Property>
      </PropertyDef>
      <PropertyDef name="specialRemark">
        <Property name="label">特殊生产要求备注</Property>
      </PropertyDef>
    </DataType>
    <DataType name="dtBlXgjq">
      <PropertyDef name="blHfjq">
        <Property name="dataType">Date</Property>
        <Property name="label">回复交期</Property>
      </PropertyDef>
      <PropertyDef name="cause">
        <Property name="dataType">String</Property>
        <Property name="label">原因</Property>
        <Property name="mapping">
          <Property name="keyProperty">enumvCode</Property>
          <Property name="valueProperty">enumvName</Property>
          <Property name="mapValues">${dorado.getDataProvider(&quot;enumController#queryEnumv&quot;).getResult(&quot;updatecause&quot;)}</Property>
        </Property>
      </PropertyDef>
      <PropertyDef name="remarkDate">
        <Property name="dataType">String</Property>
        <Property name="label">备注</Property>
      </PropertyDef>
    </DataType>
  </Model>
  <View>
    <ClientEvent name="onReady">var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
&#xD;
dsSearch.set('parameter', {&#xD;
	trbs :'${param.trbs}',&#xD;
	status2 : 'gl',&#xD;
	p2 :'${param.sdf2}',&#xD;
	lx:'SP'&#xD;
});&#xD;
&#xD;
&#xD;
</ClientEvent>
    <DataSet id="dsCondition">
      <ClientEvent name="onReady">var dateNow = new Date()&#xD;
dateNow.setDate(dateNow.getDate() - 30)&#xD;
&#xD;
var dateNowTo = new Date()&#xD;
dateNowTo.setDate(dateNowTo.getDate() + 1)&#xD;
&#xD;
self.insert({&#xD;
	ORDER_DATE_B : dateNow,&#xD;
	ORDER_DATE_E : dateNowTo&#xD;
});</ClientEvent>
      <Property name="dataType">dtCondition</Property>
    </DataSet>
    <DataSet id="dsSearch">
      <ClientEvent name="onLoadData">// var  sel  = self.getData();&#xD;
//var ddh =&quot;&quot;;&#xD;
// sel.each(function(item) {&#xD;
//	if(item.get('hsbzlh').substring(0,1)=='2'){&#xD;
//		item.set('bzkc',toFixed(item.get('xdsl')-item.get('bbkc'),3));&#xD;
///	}else{&#xD;
//		item.set('bzkc',item.get('xdsl')-item.get('cpkc')-item.get('ghkc'));&#xD;
//	}&#xD;
//	});&#xD;
//		function toFixed(number,fractionDigits){&#xD;
//   var times = Math.pow(10, fractionDigits);&#xD;
//    var roundNum = Math.round(number * times) / times;&#xD;
//    return roundNum.toFixed(fractionDigits);&#xD;
//}</ClientEvent>
      <Property name="loadMode">manual</Property>
      <Property name="dataType">[dtSearch]</Property>
      <Property name="readOnly">false</Property>
      <Property name="dataProvider">orderSaleController#queryReviewOrderSaleChip</Property>
      <Property name="pageSize">200</Property>
    </DataSet>
    <DataSet id="dsBlSearch">
      <ClientEvent name="onReady">self.insert({&#xD;
	createBy :'${loginUser.getUsername()}'&#xD;
});</ClientEvent>
      <Property name="dataType">[dtBlSearch]</Property>
    </DataSet>
    <DataSet id="dsBl">
      <Property name="dataType">[dtBl]</Property>
      <Property name="pageSize">100</Property>
      <Property name="loadMode">manual</Property>
      <Property name="dataProvider">orderSpareChipController#queryByParamsSg</Property>
    </DataSet>
    <DataSet id="dsBlXgjq">
      <ClientEvent name="onReady">self.insert();&#xD;
</ClientEvent>
      <Property name="dataType">[dtBlXgjq]</Property>
    </DataSet>
    <TabControl>
      <Property name="lazyInit">true</Property>
      <ControlTab>
        <Property name="caption">销售订单评审</Property>
        <Container>
          <FieldSet>
            <Property name="caption">查询条件</Property>
            <Property name="hideMode">display</Property>
            <Buttons/>
            <Children>
              <Container layout="hbox">
                <AutoForm id="autoFromReview">
                  <Property name="cols">300,300,300,300</Property>
                  <Property name="cols">270,270,270</Property>
                  <Property name="dataSet">dsCondition</Property>
                  <Property name="labelWidth">100</Property>
                  <Property name="labelAlign">right</Property>
                  <Property name="readOnly">false</Property>
                  <Property name="labelSeparator">:</Property>
                  <AutoFormElement>
                    <ClientEvent name="onKeyDown">if(arg.keyCode==13){&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
dsSearch.set('parameter', dsCond.getData());&#xD;
dsSearch.flushAsync();&#xD;
&#xD;
&#xD;
	&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
}&#xD;
</ClientEvent>
                    <Property name="name">ddh</Property>
                    <Property name="property">ddh</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">ORDER_DATE_B</Property>
                    <Property name="property">ORDER_DATE_B</Property>
                    <Property name="trigger">defaultDateTimeDropDown</Property>
                    <Property name="label">${res[&quot;field/field.common.orderCreateTime&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <ClientEvent name="onKeyDown">if(arg.keyCode==13){&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
dsSearch.set('parameter', dsCond.getData());&#xD;
dsSearch.flushAsync();&#xD;
&#xD;
&#xD;
	&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
}&#xD;
</ClientEvent>
                    <Property name="name">ORDER_DATE_E</Property>
                    <Property name="property">ORDER_DATE_E</Property>
                    <Property name="trigger">defaultDateTimeDropDown</Property>
                    <Property name="label">${res[&quot;field/filed.common.to&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <ClientEvent name="onKeyDown">if(arg.keyCode==13){&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
dsSearch.set('parameter', dsCond.getData());&#xD;
dsSearch.flushAsync();&#xD;
&#xD;
&#xD;
	&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
}&#xD;
</ClientEvent>
                    <Property name="name">p4</Property>
                    <Property name="property">p4</Property>
                    <Property name="label">物料号</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">JH_DATE_B</Property>
                    <Property name="property">JH_DATE_B</Property>
                    <Property name="trigger">defaultDateTimeDropDown</Property>
                    <Property name="label">${res[&quot;field/field.common.replyDate&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <ClientEvent name="onKeyDown">if(arg.keyCode==13){&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
dsSearch.set('parameter', dsCond.getData());&#xD;
dsSearch.flushAsync();&#xD;
&#xD;
&#xD;
	&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
}&#xD;
</ClientEvent>
                    <Property name="name">JH_DATE_E</Property>
                    <Property name="property">JH_DATE_E</Property>
                    <Property name="trigger">defaultDateTimeDropDown</Property>
                    <Property name="label">${res[&quot;field/field.common.to&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <ClientEvent name="onKeyDown">if(arg.keyCode==13){&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
dsSearch.set('parameter', dsCond.getData());&#xD;
dsSearch.flushAsync();&#xD;
&#xD;
&#xD;
	&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
}&#xD;
</ClientEvent>
                    <Property name="name">p1</Property>
                    <Property name="property">p1</Property>
                    <Property name="label">${res[&quot;field/field.common.sellTo&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">requireDateFrom</Property>
                    <Property name="property">requireDateFrom</Property>
                    <Property name="label">${res[&quot;field/field.common.requireDate&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">requireDateTo</Property>
                    <Property name="property">requireDateTo</Property>
                    <Property name="label">${res[&quot;field/field.common.to&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <ClientEvent name="onKeyDown">if(arg.keyCode==13){&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
dsSearch.set('parameter', dsCond.getData());&#xD;
dsSearch.flushAsync();&#xD;
&#xD;
&#xD;
	&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
}&#xD;
</ClientEvent>
                    <Property name="name">p2</Property>
                    <Property name="property">p2</Property>
                    <Property name="label">${res[&quot;field/field.common.shipTo&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <ClientEvent name="onKeyDown">if(arg.keyCode==13){&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
dsSearch.set('parameter', dsCond.getData());&#xD;
dsSearch.flushAsync();&#xD;
&#xD;
&#xD;
	&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
}&#xD;
</ClientEvent>
                    <Property name="name">p8</Property>
                    <Property name="property">p8</Property>
                    <Property name="label">${res[&quot;field/field.common.produceNo&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="property">createdby</Property>
                    <Property name="name">createdby</Property>
                    <Property name="label">${res[&quot;field/field.common.creater&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <!--<AutoFormElement>
                    <Property name="name">whetherMS</Property>
                    <Property name="property">whetherMS</Property>
                    <Property name="trigger">listDropDown1</Property>
                    <Property name="label">${res[&quot;field/field.common.whetherMS&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>-->
                  <AutoFormElement>
                    <Property name="name">fhd</Property>
                    <Property name="property">fhd</Property>
                    <Property name="label">${res[&quot;field/field.common.deliveryPlace&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">stockStatus</Property>
                    <Property name="property">stockStatus</Property>
                    <Property name="label">${res[&quot;field/field.common.stockStatus&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">status</Property>
                    <Property name="property">status</Property>
                    <Property name="label">${res[&quot;field/field.common.status&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                </AutoForm>
              </Container>
            </Children>
          </FieldSet>
          <ToolBar>
            <Property name="hideMode">visibility</Property>
            <DataPilot>
              <Property name="dataSet">dsSearch</Property>
              <Property name="itemCodes">pages,pageSize</Property>
            </DataPilot>
            <Separator/>
            <ToolBarButton id="btnSeach">
              <ClientEvent name="onClick" signature="self,arg,autoFromReview">var entity=autoFromReview.get(&quot;entity&quot;);&#xD;
if(entity._data.ORDER_DATE_B == null &amp;&amp; entity._data.JH_DATE_B == null){&#xD;
	dorado.MessageBox.alert('下单日期、回复交期 不能都为空')&#xD;
	return&#xD;
}&#xD;
&#xD;
var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
dsSearch.set('parameter', dsCond.getData());&#xD;
dsSearch.flushAsync();&#xD;
&#xD;
&#xD;
	&#xD;
&#xD;
&#xD;
&#xD;
</ClientEvent>
              <Property name="icon">url(>skin>common/icons.gif) -40px -40px</Property>
              <Property name="caption">${res[&quot;field/btn.common.search&quot;]}</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton id="validateInventory">
              <ClientEvent name="onClick">view.id('updateds').execute(function (item){&#xD;
	alert(item);&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
	var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
	&#xD;
	dsSearch.set('parameter', dsCond.getData());&#xD;
	dsSearch.flushAsync();&#xD;
});</ClientEvent>
              <Property name="icon">url(>skin>common/icons.gif) -20px -200px</Property>
              <Property name="caption">点库存</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton>
              <ClientEvent name="onClick">view.id('dlgxgjq').show();&#xD;
</ClientEvent>
              <Property name="icon">url(>skin>common/icons.gif) -20px -200px</Property>
              <Property name="caption">批量修改交期</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton id="btnReset5">
              <ClientEvent name="onClick" signature="self,arg,dgSearch">view.id('updateds4').execute(function (e){&#xD;
	&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
	var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
	dsSearch.set('parameter', dsCond.getData());&#xD;
	dsSearch.flushAsync();&#xD;
});&#xD;
</ClientEvent>
              <Property name="icon">url(>skin>common/icons.gif) -20px -200px</Property>
              <Property name="caption">评审完成</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton id="recall">
              <ClientEvent name="onClick" signature="self,arg,dgSearch">var data= dgSearch.get('selection');&#xD;
var flag = true;&#xD;
&#xD;
data.each(function(item){&#xD;
	if(item.get('status') > 2 ){&#xD;
		dorado.MessageBox.alert(&quot;订单号:&quot; + item.get('orderNo') + &quot;项目:&quot; + item.get('orderItem') + &quot;已传SAP,不可退回!&quot;);&#xD;
		flag = false;&#xD;
	}&#xD;
});&#xD;
&#xD;
if(flag){&#xD;
	&#xD;
}&#xD;
view.id('dlgtuihui').show();&#xD;
		</ClientEvent>
              <Property name="icon">url(>skin>common/icons.gif) -20px -200px</Property>
              <Property name="caption">${res[&quot;field/btn.common.sendBack&quot;]}</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton id="btnImport">
              <Property name="icon">url(>skin>common/icons.gif) -40px -100px</Property>
              <Property name="caption">${res[&quot;field/btn.common.exportExcel&quot;]}</Property>
              <Property name="action">export</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton id="btCgjh">
              <ClientEvent name="onClick" signature="self,arg,dgSearch">var data= dgSearch.get('selection');&#xD;
var flag = true;&#xD;
&#xD;
data.each(function(item){&#xD;
	if(item.get('supplyType') != 1 ){&#xD;
		flag = false;&#xD;
		return false;&#xD;
	}&#xD;
});&#xD;
&#xD;
if(flag){&#xD;
	view.id('updateCgjh').execute(function (e){&#xD;
		alert(e);&#xD;
		var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
		var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
	&#xD;
		dsSearch.set('parameter', dsCond.getData());&#xD;
		dsSearch.flushAsync();&#xD;
	});&#xD;
}else{&#xD;
	dorado.MessageBox.alert(&quot;所选记录中,有不是外调的订单!&quot;);&#xD;
}</ClientEvent>
              <Property name="caption">${res[&quot;field/btn.common.releaseProcurementPlan&quot;]}</Property>
              <Property name="icon">url(>skin>common/icons.gif) -20px -200px</Property>
              <Property name="visible">false</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton id="btNoScjh">
              <ClientEvent name="onClick" signature="self,arg,dgSearch">var data= dgSearch.get('selection');&#xD;
var flag = true;&#xD;
&#xD;
data.each(function(item){&#xD;
	if(item.get('orderType') != 'ZDDQ' ){&#xD;
		flag = false;&#xD;
		return false;&#xD;
	}&#xD;
});&#xD;
&#xD;
if(flag){&#xD;
	view.id('updateNoScjh').execute(function (e){&#xD;
		alert(e);&#xD;
		var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
		var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
	&#xD;
		dsSearch.set('parameter', dsCond.getData());&#xD;
		dsSearch.flushAsync();&#xD;
	});&#xD;
}else{&#xD;
	dorado.MessageBox.alert(&quot;只有ZDDQ类型的订单可以不下生产计划!&quot;);&#xD;
}&#xD;
&#xD;
	</ClientEvent>
              <Property name="caption">${res[&quot;field/btn.common.noProductionPlan&quot;]}</Property>
              <Property name="icon">url(>skin>common/icons.gif) -20px -200px</Property>
              <Property name="visible">false</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton>
              <ClientEvent name="onClick" signature="self,arg,updateDycForOnlyTag">updateDycForOnlyTag.execute(function(){&#xD;
var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
dsSearch.set('parameter', dsCond.getData());&#xD;
dsSearch.flushAsync();	&#xD;
});&#xD;
&#xD;
&#xD;
</ClientEvent>
              <Property name="caption">${res[&quot;field/btn.common.pointPrediction&quot;]}</Property>
              <Property name="icon">url(>skin>common/icons.gif) -0px -200px</Property>
              <Property name="visible">false</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton id="btnReset6">
              <ClientEvent name="onClick" signature="self,arg,dgSearch">view.id('updateds5').execute(function (e){&#xD;
	&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
	var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
&#xD;
	dsSearch.set('parameter', dsCond.getData());&#xD;
	dsSearch.flushAsync();&#xD;
});&#xD;
</ClientEvent>
              <Property name="icon">url(>skin>common/icons.gif) -20px -200px</Property>
              <Property name="caption">下达采购计划</Property>
            </ToolBarButton>
            <Separator/>
            <ToolBarButton>
              <ClientEvent name="onClick" signature="self,arg,dsSearch,dgSearch,orderSplit">
                var chenckItems = dgSearch.get('selection');&#xD;
                &#xD;
                if(chenckItems.length == 1 ){&#xD;
                var sel = chenckItems[0];
                orderSplit.show();&#xD;
                var dataSet = this.get(&quot;#dataSetOrderSplit&quot;).getData('#');
                dataSet.set('qty',sel.get('qty'));
                dataSet.set('orderNo',sel.get('orderNo'));
                dataSet.set('orderItem',sel.get('orderItem'));
                dataSet.set('produceNo',sel.get('produceNo'));

                }&#xD;
                else{&#xD;
                dorado.MessageBox.alert( '请勾选一条数据！' );&#xD;
                }
              </ClientEvent>
              <Property name="icon">url(>skin>common/icons.gif) -20px -200px</Property>
              <Property name="caption">订单拆分</Property>
            </ToolBarButton>
          </ToolBar>
          <DataGrid id="dgSearch">
            <Property name="allowNoCurrent">true</Property>
            <Property name="dataSet">dsSearch</Property>
            <Property name="stretchColumnsMode">off</Property>
            <Property name="showFooter">true</Property>
            <Property name="showGroupFooter">true</Property>
            <Property name="selectionMode">multiRows</Property>
            <Property name="readOnly">true</Property>
            <RowNumColumn/>
            <RowSelectorColumn id="selc"/>
            <DataColumn name="pkId">
              <Property name="property">pkId</Property>
              <Property name="visible">false</Property>
            </DataColumn>
            <DataColumn>
              <Property name="property">sync</Property>
              <Property name="name">sync</Property>
              <Property name="readOnly">true</Property>
              <Property name="width">60px</Property>
              <Property name="caption">${res[&quot;field/field.common.syncregime&quot;]}</Property>
              <Editor/>
            </DataColumn>
            <DataColumn>
              <Property name="name">msg</Property>
              <Property name="property">msg</Property>
              <Property name="width">80px</Property>
              <Property name="readOnly">true</Property>
              <Property name="caption">${res[&quot;field/field.common.syncregime&quot;]}</Property>
              <Editor/>
            </DataColumn>
            <DataColumn>
              <Property name="name">supplyType</Property>
              <Property name="property">supplyType</Property>
              <Property name="readOnly">true</Property>
              <Property name="caption">${res[&quot;field/field.common.projectType&quot;]}</Property>
              <Editor/>
            </DataColumn>
            <DataColumn name="deliveryStatus">
              <Property name="property">deliveryStatus</Property>
              <Property name="caption">${res[&quot;field/field.common.deliveryStatus&quot;]}</Property>
              <Property name="visible">false</Property>
            </DataColumn>
            <DataColumn name="headRemark">
              <Property name="property">headRemark</Property>
              <Property name="caption">${res[&quot;field/field.common.headRemark&quot;]}</Property>
            </DataColumn>
            <DataColumn name="specialRemark">
              <Property name="property">specialRemark</Property>
              <Property name="caption">特殊生产要求备注</Property>
            </DataColumn>
            <DataColumn name="autoReplyDate">
              <Property name="property">autoReplyDate</Property>
              <Property name="caption">${res[&quot;field/field.common.autoReplyDate&quot;]}</Property>
            </DataColumn>
            <DataColumn name="replyDate">
              <Property name="property">replyDate</Property>
              <Property name="caption">${res[&quot;field/field.common.replyDate&quot;]}</Property>
            </DataColumn>
            <DataColumn name="requireDate">
              <Property name="property">requireDate</Property>
              <Property name="caption">${res[&quot;field/field.common.requireDate&quot;]}</Property>
            </DataColumn>
            <DataColumn>
              <Property name="name">duedays</Property>
              <Property name="property">duedays</Property>
              <Property name="readOnly">true</Property>
              <Property name="caption">预估入库日期</Property>
              <Editor/>
            </DataColumn>
            <DataColumn>
              <Property name="name">plannedCompletionDate</Property>
              <Property name="property">plannedCompletionDate</Property>
              <Property name="readOnly">true</Property>
              <Property name="caption">计划完成日期</Property>
              <Editor/>
            </DataColumn>
            <DataColumn name="qty">
              <Property name="property">qty</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">${res[&quot;field/field.common.orderQty&quot;]}</Property>
            </DataColumn>
            <DataColumn name="bzkc">
              <Property name="property">bzkc</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">${res[&quot;field/field.common.bzkc&quot;]}</Property>
            </DataColumn>
            <DataColumn name="bbStock">
              <Property name="property">bbStock</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">BB在制量</Property>
            </DataColumn>
            <DataColumn>
              <Property name="name">ktsch</Property>
              <Property name="property">ktsch</Property>
              <Property name="readOnly">true</Property>
              <Property name="caption">制程</Property>
              <Editor/>
            </DataColumn>
            <DataColumn>
              <Property name="property">stockStatus</Property>
              <Property name="name">stockStatus</Property>
              <Property name="caption">${res[&quot;field/field.common.stockStatus&quot;]}</Property>
              <Editor/>
            </DataColumn>
            <DataColumn name="sellTo">
              <Property name="property">sellTo</Property>
              <Property name="caption">${res[&quot;field/field.common.sellTo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="customerType">
              <Property name="property">customerType</Property>
              <Property name="caption">客户类型</Property>
            </DataColumn>
            <DataColumn>
              <Property name="name">stockHouse</Property>
              <Property name="property">stockHouse</Property>
              <Property name="caption">${res[&quot;field/field.common.stockHouse&quot;]}</Property>
              <Editor/>
            </DataColumn>
            <DataColumn name="spareMark">
              <Property name="property">spareMark</Property>
              <Property name="caption">${res[&quot;field/field.common.spareMark&quot;]}</Property>
            </DataColumn>
            <DataColumn name="produceNo">
              <Property name="property">produceNo</Property>
              <Property name="caption">${res[&quot;field/field.common.produceNo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="sapNo">
              <Property name="property">sapNo</Property>
              <Property name="caption">物料号</Property>
            </DataColumn>
            <DataColumn name="beskz">
              <Property name="property">beskz</Property>
              <Property name="caption">物料采购类型</Property>
            </DataColumn>
            <DataColumn name="specifications">
              <Property name="property">specifications</Property>
              <Property name="caption">${res[&quot;field/field.common.specifications&quot;]}</Property>
            </DataColumn>
            <DataColumn name="accuracy">
              <Property name="property">accuracy</Property>
              <Property name="caption">${res[&quot;field/field.common.accuracy&quot;]}</Property>
            </DataColumn>
            <DataColumn name="resistance">
              <Property name="property">resistance</Property>
              <Property name="caption">${res[&quot;field/field.common.resistance&quot;]}</Property>
            </DataColumn>
            <DataColumn name="orderNo">
              <Property name="property">orderNo</Property>
              <Property name="caption">${res[&quot;field/field.common.orderNo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="orderItem">
              <Property name="property">orderItem</Property>
              <Property name="caption">${res[&quot;field/field.common.orderItem&quot;]}</Property>
            </DataColumn>
            <DataColumn name="status">
              <Property name="property">status</Property>
              <Property name="caption">${res[&quot;field/field.common.status&quot;]}</Property>
            </DataColumn>

            <DataColumn>
              <Property name="property">subTime</Property>
              <Property name="name">subTime</Property>
              <Property name="caption">${res[&quot;field/field.common.subTime&quot;]}</Property>
              <Editor/>
            </DataColumn>
            <DataColumn>
              <Property name="name">createTime</Property>
              <Property name="property">createTime</Property>
              <Property name="caption">${res[&quot;field/field.common.createTime&quot;]}</Property>
              <Editor/>
            </DataColumn>
            <DataColumn name="shipTo">
              <Property name="property">shipTo</Property>
              <Property name="caption">${res[&quot;field/field.common.shipTo&quot;]}</Property>
            </DataColumn>

            <DataColumn name="trStock">
              <Property name="property">trStock</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">${res[&quot;field/field.common.trStock&quot;]}</Property>
            </DataColumn>

            <DataColumn>
              <Property name="name">mrkb</Property>
              <Property name="property">mrkb</Property>
              <Property name="caption">${res[&quot;field/field.common.mrkb&quot;]}</Property>
              <Editor/>
            </DataColumn>


            <DataColumn name="waitBoxQty">
              <Property name="property">waitBoxQty</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">${res[&quot;field/field.common.waitBoxQty&quot;]}</Property>
            </DataColumn>
            <DataColumn name="itemRemark">
              <Property name="property">itemRemark</Property>
              <Property name="caption">${res[&quot;field/field.common.lineProjectRemark&quot;]}</Property>
            </DataColumn>

            <DataColumn name="jtdm">
              <Property name="property">jtdm</Property>
              <Property name="caption">${res[&quot;field/field.common.jtdm&quot;]}</Property>
            </DataColumn>
            <DataColumn name="zguige">
              <Property name="property">zguige</Property>
              <Property name="caption">${res[&quot;field/field.common.mainSpecifications&quot;]}</Property>
            </DataColumn>
            <DataColumn name="resisValueRange">
              <Property name="property">resisValueRange</Property>
              <Property name="caption">${res[&quot;field/field.common.resisValueRange&quot;]}</Property>
            </DataColumn>
            <DataColumn name="standardNo">
              <Property name="property">standardNo</Property>
              <Property name="caption">${res[&quot;field/field.common.standardNo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="creater">
              <Property name="property">creater</Property>
              <Property name="caption">${res[&quot;field/field.common.creater&quot;]}</Property>
            </DataColumn>
            <DataColumn name="resistanceNumber">
              <Property name="property">resistanceNumber</Property>
              <Property name="caption">${res[&quot;field/field.common.resistanceNumber&quot;]}</Property>
            </DataColumn>
            <DataColumn name="deliveryPlace">
              <Property name="property">deliveryPlace</Property>
              <Property name="caption">${res[&quot;field/field.common.deliveryPlace&quot;]}</Property>
            </DataColumn>
            <DataColumn name="dispatchType">
              <Property name="property">dispatchType</Property>
              <Property name="caption">${res[&quot;field/field.common.dispatchType&quot;]}</Property>
            </DataColumn>

            <DataColumn name="customerMaterial1">
              <Property name="property">customerMaterial1</Property>
              <Property name="caption">${res[&quot;field/field.common.customerMaterial1&quot;]}</Property>
            </DataColumn>
            <DataColumn>
              <Property name="name">sapOrderNo</Property>
              <Property name="property">sapOrderNo</Property>
              <Property name="caption">${res[&quot;field/field.common.sapOrderNo&quot;]}</Property>
              <Property name="visible">false</Property>
              <Editor/>
            </DataColumn>
            <DataColumn name="orderMark">
              <Property name="property">orderMark</Property>
              <Property name="caption">${res[&quot;field/field.common.orderMark&quot;]}</Property>
            </DataColumn>
            <DataColumn name="custPurchaseNo">
              <Property name="property">custPurchaseNo</Property>
              <Property name="caption">${res[&quot;field/field.common.custPurchaseNo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="custPurchaseItem">
              <Property name="property">custPurchaseItem</Property>
              <Property name="caption">${res[&quot;field/field.common.custPurchaseItem&quot;]}</Property>
            </DataColumn>
            <DataColumn name="orderType">
              <Property name="property">orderType</Property>
              <Property name="caption">${res[&quot;field/field.common.orderType&quot;]}</Property>
            </DataColumn>
            <DataColumn name="power">
              <Property name="property">power</Property>
              <Property name="caption">${res[&quot;field/field.common.power&quot;]}</Property>
            </DataColumn>
            <DataColumn name="replyDateFirst">
              <Property name="property">replyDateFirst</Property>
              <Property name="caption">${res[&quot;field/field.common.replyDateFirst&quot;]}</Property>
            </DataColumn>
            <DataColumn name="packType">
              <Property name="property">packType</Property>
              <Property name="caption">${res[&quot;field/field.common.packType&quot;]}</Property>
            </DataColumn>
            <DataColumn name="packQty">
              <Property name="property">packQty</Property>
              <Property name="caption">${res[&quot;field/field.common.packQty&quot;]}</Property>
            </DataColumn>
            <DataColumn name="currency">
              <Property name="property">currency</Property>
              <Property name="caption">${res[&quot;field/field.common.currency&quot;]}</Property>
            </DataColumn>
            <DataColumn name="characteristic1">
              <Property name="property">characteristic1</Property>
              <Property name="caption">${res[&quot;field/field.common.characteristic1&quot;]}</Property>
            </DataColumn>
            <DataColumn name="characteristic2">
              <Property name="property">characteristic2</Property>
              <Property name="caption">${res[&quot;field/field.common.characteristic2&quot;]}</Property>
            </DataColumn>
            <DataColumn name="ppm">
              <Property name="property">ppm</Property>
              <Property name="caption">${res[&quot;field/field.common.ppm&quot;]}</Property>
            </DataColumn>
            <DataColumn name="version">
              <Property name="property">version</Property>
              <Property name="visible">false</Property>
            </DataColumn>
            <DataColumn name="tableRemark">
              <Property name="property">tableRemark</Property>
              <Property name="visible">false</Property>
            </DataColumn>
            <DataColumn name="dispatchReplyDate">
              <Property name="property">dispatchReplyDate</Property>
              <Property name="caption">${res[&quot;field/field.common.dispatchReplyDate&quot;]}</Property>
            </DataColumn>
          </DataGrid>
          <Export2ReportAction id="export">
            <Property name="template">dgSearch</Property>
            <Property name="maxSize">30000</Property>
            <Property name="extension">xls</Property>
            <Property name="dataScope">serverAll</Property>
          </Export2ReportAction>
          <ImportExcelAction id="import1">
            <Property name="excelModelId">xsdd</Property>
            <Property name="successMessage">导入成功!</Property>
          </ImportExcelAction>
          <UpdateAction id="updateds">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgSearch">arg.data = dgSearch.get('selection');&#xD;
</ClientEvent>
            <Property name="dataResolver">inventoryController#validateInventory</Property>
            <Property name="parameter"></Property>
            <UpdateItem>
              <Property name="dataPath">!DIRTY_TREE</Property>
              <Property name="dataSet">dsSearch</Property>
            </UpdateItem>
          </UpdateAction>
          <UpdateAction id="updateds4">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgSearch">arg.data = dgSearch.get('selection');&#xD;
</ClientEvent>
            <ClientEvent name="onSuccess">var str=self.get(&quot;returnValue&quot;);&#xD;
dorado.MessageBox.alert(str);</ClientEvent>
            <Property name="dataResolver">orderSaleController#sendOrderReviewToSap</Property>
            <Property name="parameter"></Property>
            <UpdateItem>
              <Property name="dataPath">!DIRTY_TREE</Property>
              <Property name="dataSet">dsSearch</Property>
            </UpdateItem>
          </UpdateAction>
          <UpdateAction id="updateds41">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgSearch">arg.data = dgSearch.get('selection');&#xD;
</ClientEvent>
            <Property name="dataResolver">orderSaleController#sendBackOrder</Property>
            <Property name="parameter"></Property>
            <Property name="confirmMessage">您确定要退回订单吗？</Property>
            <UpdateItem>
              <Property name="dataPath">!DIRTY_TREE</Property>
              <Property name="dataSet">dsSearch</Property>
            </UpdateItem>
          </UpdateAction>
          <Dialog id="dlgtuihui">
            <Property name="height">500</Property>
            <Property name="width">750</Property>
            <Buttons>
              <Button>
                <ClientEvent name="onClick">var tuuu =view.id('tuuu').get('value');&#xD;
var dgSearch =view.id('dgSearch').get('selection');&#xD;
&#xD;
dgSearch.each(function (e){&#xD;
	e.set('returnReason',tuuu);&#xD;
});&#xD;
&#xD;
view.id('updateds41').execute(function (e){&#xD;
	var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
	var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
	&#xD;
	dsSearch.set('parameter', dsCond.getData());&#xD;
	dsSearch.flushAsync();&#xD;
});&#xD;
	&#xD;
view.id('dlgtuihui').hide();&#xD;
</ClientEvent>
                <Property name="caption">保存</Property>
              </Button>
              <Button>
                <ClientEvent name="onClick">view.id('dlgtuihui').hide();&#xD;
</ClientEvent>
                <Property name="caption">取消</Property>
              </Button>
            </Buttons>
            <Children>
              <AutoForm>
                <AutoFormElement id="tuuu">
                  <Property name="label">退回原因</Property>
                  <Property name="editorType">TextArea</Property>
                  <Editor/>
                </AutoFormElement>
              </AutoForm>
            </Children>
            <Tools/>
          </Dialog>
          <Dialog id="dlgxgjq">
            <Property name="caption">批量修改交期</Property>
            <Property name="height">200</Property>
            <Property name="width">400</Property>
            <Buttons>
              <Button>
                <ClientEvent name="onClick">view.id('dlgxgjq').hide();&#xD;
var  sel  = view.id('dgSearch').get('selection');&#xD;
                  console.log(sel._data);
&#xD;
Date.prototype.format = function(fmt) { &#xD;
     var o = { &#xD;
        &quot;M+&quot; : this.getMonth()+1,                 //月份 &#xD;
        &quot;d+&quot; : this.getDate(),                    //日 &#xD;
        &quot;h+&quot; : this.getHours(),                   //小时 &#xD;
        &quot;m+&quot; : this.getMinutes(),                 //分 &#xD;
        &quot;s+&quot; : this.getSeconds(),                 //秒 &#xD;
        &quot;q+&quot; : Math.floor((this.getMonth()+3)/3), //季度 &#xD;
        &quot;S&quot;  : this.getMilliseconds()             //毫秒 &#xD;
    }; &#xD;
    if(/(y+)/.test(fmt)) {&#xD;
            fmt=fmt.replace(RegExp.$1, (this.getFullYear()+&quot;&quot;).substr(4 - RegExp.$1.length)); &#xD;
    }&#xD;
	for(var k in o) {&#xD;
	if(new RegExp(&quot;(&quot;+ k +&quot;)&quot;).test(fmt)){&#xD;
	     fmt = fmt.replace(RegExp.$1, (RegExp.$1.length==1) ? (o[k]) : ((&quot;00&quot;+ o[k]).substr((&quot;&quot;+ o[k]).length)));&#xD;
	 }&#xD;
	}&#xD;
    return fmt; &#xD;
}        &#xD;
&#xD;
var sll =0;&#xD;
var dataSet = this.get(&quot;#dataSet1&quot;).getData('#');&#xD;
var tmpYqjq = dataSet.get('replyDate');&#xD;
var cause = dataSet.get('cause');&#xD;
var remark = dataSet.get('remark');&#xD;
var currDay = new Date();&#xD;
if(tmpYqjq == undefined || tmpYqjq == null){
    dorado.MessageBox.alert(&quot;时间不能为空&quot;);&#xD;
    return;	&#xD;
}
<!--if(cause == undefined || cause == null){-->
<!--    dorado.MessageBox.alert(&quot;原因不能为空&quot;);&#xD;-->
<!--    return;	&#xD;-->
<!--}-->

//if(tmpYqjq.format('yyyy-MM-dd')&lt;currDay.format('yyyy-MM-dd')){&#xD;
//	dorado.MessageBox.alert(&quot;交期不能早于今天&quot;);&#xD;
//	return;	&#xD;
//}&#xD;
&#xD;
&#xD;
if(cause == undefined || cause == ""){
cause = "u14";
}
if(cause == "u8" || cause == "u10" ){
      if(remark == undefined || remark == null){
      dorado.MessageBox.alert(&quot;原因为其他或者品质异常，备注不能为空&quot;);&#xD;
      return;	&#xD;
      }
}
cause = cause.match(/\d+/);
if(remark == undefined || remark == ""){
remark = "";
}

view.id('updateds6').set(&quot;parameter&quot;, {&quot;replyDate&quot; : tmpYqjq,&quot;remark&quot; : remark,&quot;cause&quot; : Number(cause[0])}).execute(function(item){&#xD;
		&#xD;
		var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
		var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
		&#xD;
		dsSearch.set('parameter', dsCond.getData());&#xD;
		dsSearch.flushAsync();&#xD;
});&#xD;
&#xD;
		&#xD;
		&#xD;
	</ClientEvent>
                <Property name="caption">确定</Property>
              </Button>
              <Button>
                <ClientEvent name="onClick">view.id('dlgxgjq').hide();&#xD;
</ClientEvent>
                <Property name="caption">关闭</Property>
              </Button>
            </Buttons>
            <Children>
              <AutoForm>
                <Property name="dataSet">dataSet1</Property>
                <Property name="cols">300</Property>
                <AutoFormElement>
                  <Property name="name">replyDate</Property>
                  <Property name="property">replyDate</Property>
                  <Editor/>
                </AutoFormElement>
                <AutoFormElement>
                  <Property name="name">cause</Property>
                  <Property name="property">cause</Property>
                  <Editor/>
                </AutoFormElement>
                <AutoFormElement>
                  <Property name="name">remark</Property>
                  <Property name="property">remark</Property>
                  <Editor/>
                </AutoFormElement>
              </AutoForm>
            </Children>
            <Tools/>
          </Dialog>
          <Dialog id="orderSplit">
            <Property name="caption">订单拆分</Property>
            <Property name="height">200</Property>
            <Property name="width">400</Property>
            <Buttons>
              <Button>
                <ClientEvent name="onClick">view.id('orderSplit').hide();&#xD;
var dataSet = this.get(&quot;#dataSetOrderSplit&quot;).getData('#');&#xD;
var pkId = dataSet.get('pkId');&#xD;
var qty = dataSet.get('qty');&#xD;

view.id('updateOrderSplit').set(&quot;parameter&quot;, {&quot;pkId&quot; : pkId,&quot;qty&quot; : qty}).execute(function(item){&#xD;
});&#xD;
&#xD;
		&#xD;
		&#xD;
	</ClientEvent>
                <Property name="caption">确定</Property>
              </Button>
              <Button>
                <ClientEvent name="onClick">view.id('dlgxgjq').hide();&#xD;
</ClientEvent>
                <Property name="caption">关闭</Property>
              </Button>
            </Buttons>
            <Children>
              <AutoForm>
                <Property name="dataSet">dataSetOrderSplit</Property>
                <!--<Property name="dataSet">dgSearch</Property>-->
                <Property name="labelWidth">100</Property>
                <Property name="cols">300</Property>
                <AutoFormElement>
                  <Property name="name">orderNo</Property>
                  <Property name="property">orderNo</Property>
                  <Property name="readOnly">true</Property>
                </AutoFormElement>
                <AutoFormElement>
                  <Property name="name">orderItem</Property>
                  <Property name="property">orderItem</Property>
                  <Property name="readOnly">true</Property>
                  <Editor/>
                </AutoFormElement>
                <AutoFormElement>
                  <Property name="name">produceNo</Property>
                  <Property name="property">produceNo</Property>
                  <Property name="readOnly">true</Property>
                  <Editor/>
                </AutoFormElement>
                <AutoFormElement>
                  <Property name="name">拆单数量</Property>
                  <Property name="property">qty</Property>
                  <Editor/>
                </AutoFormElement>
              </AutoForm>

            </Children>
            <Tools/>
          </Dialog>
          <DataSet id="dataSet1">
            <ClientEvent name="onReady">self.insert();&#xD;
</ClientEvent>
            <Property name="dataType">[dataType1]</Property>
          </DataSet>
          <DataSet id="dataSetOrderSplit">
            <ClientEvent name="onReady">self.insert();&#xD;
            </ClientEvent>
            <Property name="dataType">[dtSearch]</Property>
          </DataSet>
          <UpdateAction id="updateds6">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgSearch">arg.data = dgSearch.get('selection');&#xD;
</ClientEvent>
            <Property name="dataResolver">orderSaleController#modifyReplyDate</Property>
            <Property name="parameter"></Property>
            <UpdateItem>
              <Property name="dataPath">!DIRTY_TREE</Property>
              <Property name="dataSet">dsSearch</Property>
            </UpdateItem>
          </UpdateAction>
          <UpdateAction id="updateOrderSplit">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgSearch">arg.data = dgSearch.get('selection')[0];&#xD;
</ClientEvent>
            <ClientEvent name="onSuccess">var str=self.get(&quot;returnValue&quot;);&#xD;
              if(str!=null&amp;&amp;str!=&quot;&quot;){
                dorado.MessageBox.alert(str);&#xD;
              }else{&#xD;
                dorado.MessageBox.alert('订单拆分成功');&#xD;
                var dsSearch=view.id(&quot;dsSearch&quot;);&#xD;
                var dsCond=view.id(&quot;dsCondition&quot;);&#xD;
                &#xD;
                dsSearch.set('parameter', dsCond.getData());&#xD;
                dsSearch.flushAsync();&#xD;
              }
            </ClientEvent>
            <Property name="dataResolver">orderSaleController#orderSplit</Property>
            <Property name="parameter"></Property>
            <UpdateItem>
              <Property name="dataPath">!DIRTY_TREE</Property>
              <Property name="dataSet">dsSearch</Property>
            </UpdateItem>
          </UpdateAction>
          <UpdateAction id="updateDycForOnlyTag">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgSearch">arg.data = dgSearch.get('selection');&#xD;
</ClientEvent>
            <Property name="dataResolver">orderSaleController#updateForOnlytag</Property>
            <Property name="parameter"></Property>
            <UpdateItem>
              <Property name="dataPath">!DIRTY_TREE</Property>
              <Property name="dataSet">dsSearch</Property>
            </UpdateItem>
          </UpdateAction>
          <UpdateAction id="updateCgjh">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgSearch">arg.data = dgSearch.get('selection');</ClientEvent>
            <ClientEvent name="onSuccess">var str=self.get(&quot;returnValue&quot;);&#xD;
dorado.MessageBox.alert(str);</ClientEvent>
            <Property name="dataResolver">orderSaleController#releasePurchasePlan</Property>
            <UpdateItem>
              <Property name="dataSet">dsSearch</Property>
            </UpdateItem>
          </UpdateAction>
          <UpdateAction id="updateNoScjh">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgSearch">arg.data = dgSearch.get('selection');</ClientEvent>
            <ClientEvent name="onSuccess">var str=self.get(&quot;returnValue&quot;);&#xD;
dorado.MessageBox.alert(str);</ClientEvent>
            <Property name="dataResolver">orderSaleController#unReleaseProductionPlan</Property>
            <UpdateItem>
              <Property name="dataSet">dsSearch</Property>
            </UpdateItem>
          </UpdateAction>
          <UpdateAction id="updateds5">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgSearch">arg.data = dgSearch.get('selection');&#xD;
</ClientEvent>
            <ClientEvent name="onSuccess">var str=self.get(&quot;returnValue&quot;);&#xD;
dorado.MessageBox.alert(str);</ClientEvent>
            <Property name="dataResolver">orderSaleController#sendOrderReviewToWorkReport</Property>
            <Property name="parameter"></Property>
            <UpdateItem>
              <Property name="dataPath">!DIRTY_TREE</Property>
              <Property name="dataSet">dsSearch</Property>
            </UpdateItem>
          </UpdateAction>
        </Container>
      </ControlTab>
      <ControlTab>
        <Property name="caption">备料订单评审</Property>
        <Container>
          <FieldSet>
            <Property name="caption">查询条件</Property>
            <Property name="hideMode">display</Property>
            <Buttons/>
            <Children>
              <Container layout="hbox">
                <AutoForm id="autoFormBl">
                  <Property name="cols">270,270,270,270</Property>
                  <Property name="dataSet">dsBlSearch</Property>
                  <Property name="labelWidth">100</Property>
                  <Property name="labelAlign">right</Property>
                  <Property name="readOnly">false</Property>
                  <Property name="labelSeparator">：</Property>
                  <Property name="editorWidth">200</Property>
                  <AutoFormElement>
                    <Property name="name">orderSpareNo</Property>
                    <Property name="property">orderSpareNo</Property>
                    <Property name="label">${res[&quot;field/field.common.orderSpareNo&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">orderSpareItem</Property>
                    <Property name="property">orderSpareItem</Property>
                    <Property name="label">${res[&quot;field/field.common.orderSpareNo&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">createStart</Property>
                    <Property name="property">createStart</Property>
                    <Property name="label">${res[&quot;field/field.common.createStart&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">createEnd</Property>
                    <Property name="property">createEnd</Property>
                    <Property name="label">${res[&quot;field/field.common.to&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">producePn</Property>
                    <Property name="property">producePn</Property>
                    <Property name="label">${res[&quot;field/field.common.produceNo&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">hfjqStart</Property>
                    <Property name="property">hfjqStart</Property>
                    <Property name="label">${res[&quot;field/field.common.replyDate&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">hfjqEnd</Property>
                    <Property name="property">hfjqEnd</Property>
                    <Property name="label">${res[&quot;field/field.common.to&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">sellTo</Property>
                    <Property name="property">sellTo</Property>
                    <Property name="label">${res[&quot;field/field.common.sellTo&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">shipTo</Property>
                    <Property name="property">shipTo</Property>
                    <Property name="label">${res[&quot;field/field.common.shipTo&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">sapPn</Property>
                    <Property name="property">sapPn</Property>
                    <Property name="label">物料号</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">creater</Property>
                    <Property name="property">creater</Property>
                    <Property name="label">${res[&quot;field/field.common.creater&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <!--<AutoFormElement>
                    <Property name="name">MS和TC规格:</Property>
                    <Property name="property">whetherMS</Property>
                    <Property name="trigger">listDropDown1</Property>
                    <Editor/>
                  </AutoFormElement>-->
                  <AutoFormElement>
                    <Property name="name">deliveryFactory</Property>
                    <Property name="property">deliveryFactory</Property>
                    <Property name="label">${res[&quot;field/field.common.deliveryPlace&quot;]}</Property>
                    <Editor/>
                  </AutoFormElement>
                  <AutoFormElement>
                    <Property name="name">stockStatus</Property>
                    <Property name="property">stockStatus</Property>
                    <Editor/>
                  </AutoFormElement>
                </AutoForm>
              </Container>
            </Children>
          </FieldSet>
          <ToolBar>
            <DataPilot>
              <Property name="itemCodes">pages,pageSize</Property>
              <Property name="dataSet">dsBl</Property>
            </DataPilot>
            <ToolBarButton id="btnBlSearch">
              <ClientEvent name="onClick" signature="self,arg,autoFormBl,dsBl">var entity=autoFormBl.get(&quot;entity&quot;);&#xD;
dsBl.set(&quot;parameter&quot;,entity).flushAsync();&#xD;
</ClientEvent>
              <Property name="caption">查询</Property>
              <Property name="icon"> url(>skin>common/icons.gif) -40px -100px</Property>
              <Property name="tip">查询</Property>
            </ToolBarButton>
            <ToolBarButton id="btnBlExport">
              <Property name="icon">url(>skin>common/icons.gif) -40px -100px</Property>
              <Property name="caption">${res[&quot;field/btn.common.exportExcel&quot;]}</Property>
              <Property name="action">blExport</Property>
            </ToolBarButton>
            <ToolBarButton id="btnBlPx">
              <ClientEvent name="onClick" signature="self,arg,dsBl,autoFormBl,blpx">blpx.execute(function (item){&#xD;
&#xD;
	var entity=autoFormBl.get(&quot;entity&quot;);&#xD;
	dsBl.set(&quot;parameter&quot;,entity).flushAsync();&#xD;
});</ClientEvent>
              <Property name="caption">备料评审</Property>
              <Property name="icon">url(>skin>common/icons.gif) -20px -20px</Property>
            </ToolBarButton>
            <ToolBarButton>
              <ClientEvent name="onClick">var entity =view.id('dgBl').get('selection');&#xD;
if(entity.length == 0 ){&#xD;
	dorado.MessageBox.alert( '请先选中一条数据！' );&#xD;
}else{&#xD;
    view.id('dlgBlXgjq').show();&#xD;
}&#xD;
</ClientEvent>
              <Property name="caption">批量修改交期</Property>
              <Property name="icon">url(>skin>common/icons.gif) -20px -200px</Property>
            </ToolBarButton>
            <ToolBarButton>
              <ClientEvent name="onClick" signature="self,arg,UAReviewEnd,autoFormBl,dsBl">UAReviewEnd.execute(function (item){&#xD;
	alert(item);&#xD;
	var entity=autoFormBl.get(&quot;entity&quot;);&#xD;
	dsBl.set(&quot;parameter&quot;,entity).flushAsync();&#xD;
});</ClientEvent>
              <Property name="caption">评审结束</Property>
              <Property name="icon">url(>skin>common/icons.gif) -20px -20px</Property>
            </ToolBarButton>
            <ToolBarButton>
              <ClientEvent name="onClick" signature="self,arg,UARecall,autoFormBl,dsBl">UARecall.execute(function (item){&#xD;
	alert(item);&#xD;
	var entity=autoFormBl.get(&quot;entity&quot;);&#xD;
	dsBl.set(&quot;parameter&quot;,entity).flushAsync();&#xD;
});</ClientEvent>
              <Property name="icon">url(>skin>common/icons.gif) -220px -240px</Property>
              <Property name="caption">退回</Property>
            </ToolBarButton>
          </ToolBar>
          <DataGrid id="dgBl">
            <Property name="dataSet">dsBl</Property>
            <Property name="readOnly">true</Property>
            <Property name="showFilterBar">false</Property>
            <Property name="allowNoCurrent">true</Property>
            <Property name="autoCreateColumns">false</Property>
            <Property name="stretchColumnsMode">off</Property>
            <Property name="selectionMode">multiRows</Property>
            <Property name="showFooter">true</Property>
            <RowSelectorColumn/>
            <DataColumn name="pkId">
              <Property name="property">pkId</Property>
              <Property name="visible">false</Property>
            </DataColumn>
            <DataColumn name="status">
              <Property name="property">status</Property>
              <Property name="align">center</Property>
              <Property name="caption">${res[&quot;field/field.common.status&quot;]}</Property>
            </DataColumn>
            <DataColumn name="stockStatus">
              <Property name="property">stockStatus</Property>
              <Property name="caption">${res[&quot;field/field.common.stockStatus&quot;]}</Property>
            </DataColumn>
            <DataColumn name="orderSpareNo">
              <Property name="property">orderSpareNo</Property>
              <Property name="caption">${res[&quot;field/field.common.orderSpareNo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="orderSpareItem">
              <Property name="property">orderSpareItem</Property>
              <Property name="caption">${res[&quot;field/field.common.orderSpareItem&quot;]}</Property>
            </DataColumn>
            <DataColumn name="deliveryStatus">
              <Property name="property">deliveryStatus</Property>
              <Property name="caption">${res[&quot;field/field.common.deliveryStatus&quot;]}</Property>
              <Property name="visible">false</Property>
            </DataColumn>
            <DataColumn name="custPurchaseNo">
              <Property name="property">custPurchaseNo</Property>
              <Property name="caption">${res[&quot;field/field.common.custPurchaseNo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="custPurchaseItem">
              <Property name="property">custPurchaseItem</Property>
              <Property name="caption">${res[&quot;field/field.common.custPurchaseNo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="sellTo">
              <Property name="property">sellTo</Property>
              <Property name="caption">${res[&quot;field/field.common.sellTo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="shipTo">
              <Property name="property">shipTo</Property>
              <Property name="caption">${res[&quot;field/field.common.shipTo&quot;]}</Property>
            </DataColumn>
            <DataColumn name="requireDate">
              <Property name="property">requireDate</Property>
              <Property name="caption">${res[&quot;field/field.common.requireDate&quot;]}</Property>
            </DataColumn>
            <DataColumn name="replyDate">
              <Property name="property">replyDate</Property>
              <Property name="caption">${res[&quot;field/field.common.replyDate&quot;]}</Property>
            </DataColumn>
            <DataColumn name="customerPn">
              <Property name="property">customerPn</Property>
              <Property name="caption">${res[&quot;field/field.common.customerMaterial1&quot;]} </Property>
            </DataColumn>
            <DataColumn name="sapPn">
              <Property name="property">sapPn</Property>
              <Property name="caption">物料号</Property>
            </DataColumn>
            <DataColumn name="producePn">
              <Property name="property">producePn</Property>
              <Property name="caption">${res[&quot;field/field.common.produceNo&quot;]} </Property>
            </DataColumn>
            <DataColumn name="standardPn">
              <Property name="property">standardPn</Property>
              <Property name="caption">${res[&quot;field/field.common.standardNo&quot;]} </Property>
            </DataColumn>
            <DataColumn name="originNum">
              <Property name="property">originNum</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">${res[&quot;field/filed.common.originNum&quot;]} </Property>
            </DataColumn>
            <DataColumn name="leftNum">
              <Property name="property">leftNum</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">${res[&quot;field/filed.common.leftNum&quot;]} </Property>
            </DataColumn>
            <DataColumn name="quantity">
              <Property name="property">quantity</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">${res[&quot;field/filed.common.quantity&quot;]} </Property>
            </DataColumn>
            <DataColumn name="cancelQuantity">
              <Property name="property">cancelQuantity</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">${res[&quot;field/filed.common.cancelQuantity&quot;]} </Property>
            </DataColumn>
            <DataColumn name="trStock">
              <Property name="property">trStock</Property>
              <Property name="visible">true</Property>
              <Property name="summaryType">sum</Property>
            </DataColumn>
            <DataColumn name="bbStock">
              <Property name="property">bbStock</Property>
              <Property name="visible">true</Property>
              <Property name="summaryType">sum</Property>
              <Property name="caption">BB在制量</Property>
            </DataColumn>
            <DataColumn>
              <Property name="name">ktsch</Property>
              <Property name="property">ktsch</Property>
              <Property name="readOnly">true</Property>
              <Property name="caption">制程</Property>
              <Editor/>
            </DataColumn>
            <DataColumn>
              <Property name="name">duedays</Property>
              <Property name="property">duedays</Property>
              <Property name="readOnly">true</Property>
              <Property name="caption">预估入库日期</Property>
              <Editor/>
            </DataColumn>
            <DataColumn>
              <Property name="name">plannedCompletionDate</Property>
              <Property name="property">plannedCompletionDate</Property>
              <Property name="readOnly">true</Property>
              <Property name="caption">计划完成日期</Property>
              <Editor/>
            </DataColumn>
            <DataColumn name="trProduceNum">
              <Property name="property">trProduceNum</Property>
              <Property name="visible">false</Property>
              <Property name="summaryType">sum</Property>
            </DataColumn>
            <DataColumn name="bbProduceNum">
              <Property name="property">bbProduceNum</Property>
              <Property name="visible">false</Property>
              <Property name="summaryType">sum</Property>
            </DataColumn>
            <DataColumn name="specsName">
              <Property name="property">specsName</Property>
              <Property name="caption">${res[&quot;field/field.common.specsName&quot;]} </Property>
            </DataColumn>
            <DataColumn name="power">
              <Property name="property">power</Property>
              <Property name="caption">${res[&quot;field/field.common.power&quot;]} </Property>
            </DataColumn>
            <DataColumn name="accuracy">
              <Property name="property">accuracy</Property>
              <Property name="caption">${res[&quot;field/field.common.accuracy&quot;]} </Property>
            </DataColumn>
            <DataColumn name="resistance">
              <Property name="property">resistance</Property>
              <Property name="caption">${res[&quot;field/field.common.resistance&quot;]} </Property>
            </DataColumn>
            <DataColumn name="resistanceValue">
              <Property name="property">resistanceValue</Property>
              <Property name="caption">${res[&quot;field/field.common.resistanceNumber&quot;]} </Property>
            </DataColumn>
            <DataColumn name="packQuantity">
              <Property name="property">packQuantity</Property>
              <Property name="caption">${res[&quot;field/field.common.packQty&quot;]} </Property>
            </DataColumn>
            <DataColumn name="packType">
              <Property name="property">packType</Property>
              <Property name="caption">${res[&quot;field/field.common.packType&quot;]} </Property>
            </DataColumn>
            <DataColumn name="deliveryFactory">
              <Property name="property">deliveryFactory</Property>
              <Property name="caption">${res[&quot;field/filed.common.deliveryFactory&quot;]} </Property>
            </DataColumn>
            <DataColumn name="message">
              <Property name="property">message</Property>
              <Property name="caption">${res[&quot;field/filed.common.message&quot;]} </Property>
            </DataColumn>
            <DataColumn name="remark">
              <Property name="property">remark</Property>
              <Property name="caption">${res[&quot;field/field.common.remarks&quot;]} </Property>
            </DataColumn>
            <DataColumn name="specialRemark">
              <Property name="property">specialRemark</Property>
              <Property name="caption">特殊生产要求备注</Property>
            </DataColumn>
            <DataColumn name="submitTime">
              <Property name="property">submitTime</Property>
              <Property name="caption">${res[&quot;field/field.common.subTime&quot;]} </Property>
            </DataColumn>
            <DataColumn name="syncer">
              <Property name="property">syncer</Property>
              <Property name="caption">${res[&quot;field/field.common.syncer&quot;]} </Property>
            </DataColumn>
            <DataColumn name="creater">
              <Property name="property">creater</Property>
              <Property name="caption">${res[&quot;field/field.common.creater&quot;]} </Property>
            </DataColumn>
            <DataColumn name="createTime">
              <Property name="property">createTime</Property>
              <Property name="caption">${res[&quot;field/field.common.createTime&quot;]} </Property>
            </DataColumn>
            <DataColumn name="version">
              <Property name="property">version</Property>
              <Property name="visible">false</Property>
            </DataColumn>
            <DataColumn name="price">
              <Property name="property">price</Property>
            </DataColumn>
            <DataColumn name="currency">
              <Property name="property">currency</Property>
            </DataColumn>
            <DataColumn name="amount">
              <Property name="property">amount</Property>
            </DataColumn>
          </DataGrid>
          <UpdateAction id="blpx">
            <ClientEvent name="onGetUpdateData">arg.data=view.id('dgBl').get('selection');</ClientEvent>
            <ClientEvent name="onSuccess" signature="self,arg,autoFormBl,dsBl">var str=self.get(&quot;returnValue&quot;); &#xD;
if(str!=null&amp;&amp;str!=&quot;&quot;){&#xD;
	dorado.MessageBox.alert(str);&#xD;
}&#xD;
&#xD;
var entity=autoFormBl.get(&quot;entity&quot;);&#xD;
dsBl.set(&quot;parameter&quot;,entity).flushAsync();&#xD;
</ClientEvent>
            <Property name="dataResolver">orderSpareChipController#checkSock</Property>
            <Property name="confirmMessage">确定评审所选数据么？</Property>
            <Property name="executingMessage">正在评审。。。。。。</Property>
            <Property name="alwaysExecute">false</Property>
            <UpdateItem>
              <Property name="dataPath">[#all]</Property>
              <Property name="dataSet">dsBl</Property>
            </UpdateItem>
          </UpdateAction>
          <Dialog id="dlgBlXgjq">
            <Property name="height">200</Property>
            <Property name="width">400</Property>
            <Property name="caption">请输入交期时间</Property>
            <Buttons>
              <Button>
                <ClientEvent name="onClick" signature="self,arg,autoFormBl,dsBl">view.id('dlgBlXgjq').hide();&#xD;
var ptblds  = view.id('dgBl').get('selection');&#xD;
var dataSet = this.get(&quot;#dsBlXgjq&quot;).getData('#');&#xD;
		&#xD;
ptblds.each(function(item) {&#xD;
    item.set('remarkDate',dataSet.get('remarkDate'));&#xD;
    item.set('cause',dataSet.get('cause'));&#xD;
    item.set('hfjq',dataSet.get('blHfjq'));
});&#xD;
	&#xD;
&#xD;
view.id('UAXgjq').execute(function(item){&#xD;
	alert(item);&#xD;
	var entity=autoFormBl.get(&quot;entity&quot;);&#xD;
	dsBl.set(&quot;parameter&quot;,entity).flushAsync();&#xD;
});&#xD;
	&#xD;
		&#xD;
		&#xD;
	</ClientEvent>
                <Property name="caption">确定</Property>
              </Button>
              <Button>
                <ClientEvent name="onClick">view.id('dlgBlXgjq').hide();&#xD;
</ClientEvent>
                <Property name="caption">关闭</Property>
              </Button>
            </Buttons>
            <Children>
              <AutoForm>
                <Property name="dataSet">dsBlXgjq</Property>
                <Property name="cols">*</Property>
                <AutoFormElement>
                  <Property name="name">blHfjq</Property>
                  <Property name="property">blHfjq</Property>
                  <Editor/>
                </AutoFormElement>
                <AutoFormElement>
                  <Property name="name">cause</Property>
                  <Property name="property">cause</Property>
                  <Editor/>
                </AutoFormElement>
                <AutoFormElement>
                  <Property name="name">remarkDate</Property>
                  <Property name="property">remarkDate</Property>
                  <Editor/>
                </AutoFormElement>
              </AutoForm>
            </Children>
            <Tools/>
          </Dialog>
          <UpdateAction id="UAXgjq">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgBl">arg.data = dgBl.get('selection');&#xD;
</ClientEvent>
            <Property name="dataResolver">orderSpareChipController#updateReplyDate</Property>
            <UpdateItem>
              <Property name="dataSet">dsBl</Property>
              <Property name="dataPath">[#all]</Property>
            </UpdateItem>
          </UpdateAction>
          <UpdateAction id="UAReviewEnd">
            <ClientEvent name="onGetUpdateData" signature="self,arg,dgBl">arg.data = dgBl.get('selection');&#xD;
</ClientEvent>
            <Property name="dataResolver">orderSpareChipController#reviewEnd</Property>
            <UpdateItem>
              <Property name="dataSet">dsBl</Property>
              <Property name="dataPath">[#all]</Property>
            </UpdateItem>
          </UpdateAction>
          <UpdateAction id="UARecall">
            <ClientEvent name="onGetUpdateData">arg.data=view.id('dgBl').get('selection');</ClientEvent>
            <Property name="dataResolver">orderSpareChipController#sendBack</Property>
            <Property name="confirmMessage">确定退回所选数据么？</Property>
            <UpdateItem>
              <Property name="dataPath">[#all]</Property>
              <Property name="dataSet">dsBl</Property>
            </UpdateItem>
          </UpdateAction>
          <Export2ReportAction id="blExport">
            <Property name="template">dgBl</Property>
            <Property name="maxSize">30000</Property>
            <Property name="extension">xls</Property>
            <Property name="dataScope">currentPage</Property>
          </Export2ReportAction>
        </Container>
      </ControlTab>
    </TabControl>
    <ListDropDown id="listDropDown1">
      <Property name="items">不包含,只有</Property>
    </ListDropDown>
  </View>
</ViewConfig>
