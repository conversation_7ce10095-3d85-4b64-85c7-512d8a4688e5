<template>
  <div class="app-container">
    <!-- Tab控制器 -->
    <el-tabs v-model="activeTab" type="card" class="main-tabs" @tab-change="handleTabChange">
      <!-- 销售订单点库审核 -->
      <el-tab-pane label="销售订单点库审核" name="orderReview">
        <!-- 搜索区域 -->
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
          <div class="search" v-show="showSearch">
            <el-card shadow="never" class="search-card">
              <el-form :model="queryParams" ref="queryFormRef" :inline="false" label-width="120px">
                <!-- 基础查询条件 -->
                <div class="search-section">
                  <h4 class="section-title">基础信息</h4>
                  <el-row :gutter="16">
                    <el-col :span="6">
                      <el-form-item label="订单号" prop="ddh">
                        <el-input v-model="queryParams.ddh" placeholder="请输入订单号" clearable @keyup.enter="handleQuery" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="SAP料号" prop="p4">
                        <el-input v-model="queryParams.p4" placeholder="请输入SAP料号" clearable @keyup.enter="handleQuery" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="下单日期" prop="ORDER_DATE_B">
                        <el-date-picker 
                          v-model="queryParams.ORDER_DATE_B"
                          type="date"
                          value-format="YYYY-MM-DD"
                          placeholder="请选择下单日期"
                          style="width: 100%"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="至" prop="ORDER_DATE_E">
                        <el-date-picker 
                          v-model="queryParams.ORDER_DATE_E"
                          type="date"
                          value-format="YYYY-MM-DD"
                          placeholder="请选择结束日期"
                          style="width: 100%"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="6">
                      <el-form-item label="回复交期" prop="JH_DATE_B">
                        <el-date-picker 
                          v-model="queryParams.JH_DATE_B"
                          type="date"
                          value-format="YYYY-MM-DD"
                          placeholder="请选择回复交期"
                          style="width: 100%"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="至" prop="JH_DATE_E">
                        <el-date-picker 
                          v-model="queryParams.JH_DATE_E"
                          type="date"
                          value-format="YYYY-MM-DD"
                          placeholder="请选择结束日期"
                          style="width: 100%"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="售达方" prop="p1">
                        <el-input v-model="queryParams.p1" placeholder="请输入售达方" clearable @keyup.enter="handleQuery" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="要求交期" prop="requireDateFrom">
                        <el-date-picker 
                          v-model="queryParams.requireDateFrom"
                          type="date"
                          value-format="YYYY-MM-DD"
                          placeholder="请选择要求交期"
                          style="width: 100%"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="6">
                      <el-form-item label="至" prop="requireDateTo">
                        <el-date-picker 
                          v-model="queryParams.requireDateTo"
                          type="date"
                          value-format="YYYY-MM-DD"
                          placeholder="请选择结束日期"
                          style="width: 100%"
                          clearable
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="送达方" prop="p2">
                        <el-input v-model="queryParams.p2" placeholder="请输入送达方" clearable @keyup.enter="handleQuery" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="玖维生产料号" prop="p8">
                        <el-input v-model="queryParams.p8" placeholder="请输入玖维生产料号" clearable @keyup.enter="handleQuery" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="创建人" prop="createdby">
                        <el-input v-model="queryParams.createdby" placeholder="请输入创建人" clearable @keyup.enter="handleQuery" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="6">
                      <el-form-item label="发货地" prop="fhd">
                        <el-select v-model="queryParams.fhd" placeholder="请选择发货地" clearable style="width: 100%">
                          <el-option label="ZJ" value="ZJ" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="库存状态" prop="stockStatus">
                        <el-select v-model="queryParams.stockStatus" placeholder="请选择库存状态" clearable style="width: 100%">
                          <el-option label="成品库存" value="1" />
                          <el-option label="BB库存" value="2" />
                          <el-option label="待生产" value="3" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="6">
                      <el-form-item label="状态" prop="status">
                        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable style="width: 100%">
                          <el-option label="已评审" value="3" />
                          <el-option label="已点库" value="2" />
                          <el-option label="待评审" value="1" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 操作按钮 -->
                <div class="search-actions">
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </div>
              </el-form>
            </el-card>
          </div>
        </transition>

        <!-- 数据表格 -->
        <el-card shadow="never" class="table-card">
          <template #header>
            <div class="card-header">
              <span class="card-title">销售订单点库审核</span>
              <div class="header-actions">
                <el-button type="success" plain icon="Promotion" @click="handleSendSap">
                  传SAP
                </el-button>
                <el-button type="primary" plain icon="Box" @click="handleInventory">
                  点库
                </el-button>
                <el-button type="warning" plain icon="Process" @click="handleInProcessInventory">
                  在制点库
                </el-button>
                <el-button type="info" plain icon="Box" @click="handleSemiProductInventory">
                  半成品点库
                </el-button>
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
              </div>
            </div>
          </template>

          <el-table 
            v-loading="loading" 
            border 
            :data="saleChipList" 
            @selection-change="handleSelectionChange"
            class="data-table"
            stripe
            size="small"
          >
            <el-table-column type="selection" width="55" align="center" fixed="left" />
            
            <!-- 同步状态 -->
            <el-table-column label="同步状态" align="center" prop="sync" width="80" fixed="left">
              <template #default="scope">
                <el-tag :type="scope.row.sync === '1' ? 'success' : 'danger'" size="small">
                  {{ scope.row.sync === '1' ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            
            <!-- 同步信息 -->
            <el-table-column label="同步信息" align="center" prop="msg" width="100" show-overflow-tooltip />
            
            <!-- 计划类型 -->
            <el-table-column label="计划类型" align="center" prop="supplyType" width="100">
              <template #default="scope">
                <el-tag :type="getSupplyTypeColor(scope.row.supplyType)" size="small">
                  {{ getSupplyTypeText(scope.row.supplyType) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <!-- 交期状态 -->
            <el-table-column label="交期状态" align="center" prop="deliveryStatus" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.deliveryStatus === '0' ? 'success' : 'warning'" size="small">
                  {{ scope.row.deliveryStatus === '0' ? '可提交审核' : '审核中' }}
                </el-tag>
              </template>
            </el-table-column>
            
            <!-- 抬头备注 -->
            <el-table-column label="抬头备注" align="center" prop="headRemark" width="120" show-overflow-tooltip />
            
            <!-- 特殊生产要求备注 -->
            <el-table-column label="特殊生产要求备注" align="center" prop="specialRemark" width="150" show-overflow-tooltip />
            
            <!-- 交期信息 -->
            <el-table-column label="自动回复交期" align="center" prop="autoReplyDate" width="130">
              <template #default="scope">
                <span>{{ parseTime(scope.row.autoReplyDate, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="回复交期" align="center" prop="replyDate" width="120">
              <template #default="scope">
                <span>{{ parseTime(scope.row.replyDate, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="要求交期" align="center" prop="requireDate" width="120">
              <template #default="scope">
                <span>{{ parseTime(scope.row.requireDate, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            
            <!-- 日期信息 -->
            <el-table-column label="预估入库日期" align="center" prop="duedays" width="130" />
            <el-table-column label="计划完成日期" align="center" prop="plannedCompletionDate" width="130" />
            
            <!-- 数量信息 -->
            <el-table-column label="下单数量(KPCS)" align="center" prop="qty" width="130" />
            <el-table-column label="不足量" align="center" prop="bzkc" width="100" />
            <el-table-column label="BB在制量" align="center" prop="bbStock" width="100" />
            
            <!-- 制程信息 -->
            <el-table-column label="制程" align="center" prop="ktsch" width="80" />
            
            <!-- 库存状态 -->
            <el-table-column label="库存状态" align="center" prop="stockStatus" width="100">
              <template #default="scope">
                <el-tag :type="getStockStatusColor(scope.row.stockStatus)" size="small">
                  {{ getStockStatusText(scope.row.stockStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <!-- 客户信息 -->
            <el-table-column label="售达方" align="center" prop="sellTo" width="120" show-overflow-tooltip />
            <el-table-column label="客户类型" align="center" prop="customerType" width="100" />
            
            <!-- 库别信息 -->
            <el-table-column label="库别" align="center" prop="stockHouse" width="80" />
            
            <!-- 备料标识 -->
            <el-table-column label="备料标识" align="center" prop="spareMark" width="100">
              <template #default="scope">
                <el-tag :type="scope.row.spareMark === '1' ? 'success' : ''" size="small">
                  {{ scope.row.spareMark === '1' ? '是' : '否' }}
                </el-tag>
              </template>
            </el-table-column>
            
            <!-- 料号信息 -->
            <el-table-column label="玖维生产料号" align="center" prop="produceNo" width="150" show-overflow-tooltip />
            <el-table-column label="SAP料号" align="center" prop="sapNo" width="140" show-overflow-tooltip />
            
            <!-- 物料采购类型 -->
            <el-table-column label="物料采购类型" align="center" prop="beskz" width="120">
              <template #default="scope">
                <el-tag :type="getBeskzColor(scope.row.beskz)" size="small">
                  {{ getBeskzText(scope.row.beskz) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <!-- 产品信息 -->
            <el-table-column label="规格" align="center" prop="specifications" width="100" show-overflow-tooltip />
            <el-table-column label="精度" align="center" prop="accuracy" width="80" />
            <el-table-column label="阻值" align="center" prop="resistance" width="100" />
            
            <!-- 订单信息 -->
            <el-table-column label="订单号" align="center" prop="orderNo" width="150" show-overflow-tooltip />
            <el-table-column label="订单项目" align="center" prop="orderItem" width="100" />
            
            <!-- 状态 -->
            <el-table-column label="状态" align="center" prop="status" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)" size="small">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            
            <!-- 操作列 -->
            <el-table-column label="操作" align="center" width="120" fixed="right">
              <template #default="scope">
                <el-tooltip content="点库明细" placement="top">
                  <el-button 
                    link 
                    type="primary" 
                    icon="Box" 
                    @click="handleInventoryDetail(scope.row)"
                    size="small"
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="修改原料交期" placement="top">
                  <el-button 
                    link 
                    type="warning" 
                    icon="Edit" 
                    @click="handleEditMaterialDate(scope.row)"
                    size="small"
                  ></el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>

          <pagination 
            v-show="total > 0" 
            :total="total" 
            v-model:page="queryParams.pageNum" 
            v-model:limit="queryParams.pageSize" 
            @pagination="getList" 
          />
        </el-card>
      </el-tab-pane>

    </el-tabs>

    <!-- 点库明细对话框 -->
    <el-dialog title="点库明细" v-model="inventoryDialog.visible" width="1200px" append-to-body>
      <div class="inventory-info">
        <el-descriptions :column="3" border>
          <el-descriptions-item label="送达方">{{ inventoryForm.shipTo }}</el-descriptions-item>
          <el-descriptions-item label="订单号">{{ inventoryForm.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="SAP料号">{{ inventoryForm.sapNo }}</el-descriptions-item>
        </el-descriptions>
      </div>
      
      <div class="inventory-actions">
        <el-button type="primary" @click="queryInventory" :loading="buttonLoading">查询库存</el-button>
        <el-button type="warning" @click="noStock" :loading="buttonLoading">无库存</el-button>
      </div>
      
      <el-table :data="inventoryList" border class="inventory-table">
        <el-table-column label="仓位" align="center" prop="cw" width="100" />
        <el-table-column label="批号" align="center" prop="ph" width="120" />
        <el-table-column label="库存物料号" align="center" prop="kcwlh" width="150" />
        <el-table-column label="可用库存" align="center" prop="kykc" width="100" />
        <el-table-column label="占用数量" align="center" prop="zysl" width="120">
          <template #default="scope">
            <el-input-number 
              v-model="scope.row.zysl" 
              :min="0" 
              :max="scope.row.kykc" 
              :precision="3"
              size="small"
              style="width: 100%"
            />
          </template>
        </el-table-column>
      </el-table>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="inventoryDialog.visible = false">关闭</el-button>
          <el-button type="primary" @click="saveInventory" :loading="buttonLoading">保存</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 退回对话框 -->
    <el-dialog title="退回订单" v-model="returnDialog.visible" width="600px" append-to-body>
      <el-form :model="returnForm" label-width="100px">
        <el-form-item label="退回原因" required>
          <el-input 
            v-model="returnForm.returnReason" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入退回原因"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="returnDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmReturn" :loading="buttonLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 点库取消对话框 -->
    <el-dialog title="取消点库" v-model="cancelDialog.visible" width="600px" append-to-body>
      <el-form :model="cancelForm" label-width="100px">
        <el-form-item label="取消原因" required>
          <el-input 
            v-model="cancelForm.cancelReason" 
            type="textarea" 
            :rows="4" 
            placeholder="请输入取消原因"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmCancelInventory" :loading="buttonLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改原料交期对话框 -->
    <el-dialog title="修改原料交期" v-model="materialDateDialog.visible" width="500px" append-to-body>
      <el-form ref="materialDateFormRef" :model="materialDateForm" :rules="materialDateRules" label-width="120px">
        <el-form-item label="原料交期" prop="materialDate">
          <el-date-picker 
            v-model="materialDateForm.materialDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择原料交期"
            style="width: 100%"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="materialDateDialog.visible = false">取 消</el-button>
          <el-button type="primary" @click="confirmEditMaterialDate" :loading="buttonLoading">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量修改交期对话框 -->
    <el-dialog title="批量修改交期" v-model="batchModifyDialog.visible" width="500px" append-to-body>
      <el-form :model="batchModifyForm" label-width="100px">
        <el-form-item label="回复交期" required>
          <el-date-picker 
            v-model="batchModifyForm.replyDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请选择回复交期"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="原因">
          <el-input v-model="batchModifyForm.cause" placeholder="请输入原因" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input 
            v-model="batchModifyForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="batchModifyDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmBatchModify" :loading="buttonLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 订单拆分对话框 -->
    <el-dialog title="订单拆分" v-model="orderSplitDialog.visible" width="500px" append-to-body>
      <el-form :model="orderSplitForm" label-width="120px">
        <el-form-item label="订单号">
          <el-input v-model="orderSplitForm.orderNo" readonly />
        </el-form-item>
        <el-form-item label="订单项目">
          <el-input v-model="orderSplitForm.orderItem" readonly />
        </el-form-item>
        <el-form-item label="玖维生产料号">
          <el-input v-model="orderSplitForm.produceNo" readonly />
        </el-form-item>
        <el-form-item label="原始数量">
          <el-input-number v-model="orderSplitForm.qty" :min="0" readonly style="width: 100%" />
        </el-form-item>
        <el-form-item label="拆分数量" required>
          <el-input-number 
            v-model="orderSplitForm.splitQty" 
            :min="0" 
            :max="orderSplitForm.qty"
            :precision="3"
            style="width: 100%" 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="orderSplitDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmOrderSplit" :loading="buttonLoading">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="SaleChip">
import { ref, reactive, getCurrentInstance, onMounted } from 'vue'
import { parseTime } from '@/utils/mh'

const { proxy } = getCurrentInstance()

// 响应式数据
const activeTab = ref('orderReview')
const saleChipList = ref([])
const buttonLoading = ref(false)
const loading = ref(true)
const showSearch = ref(true)
const ids = ref([])
const total = ref(0)

// 对话框状态
const inventoryDialog = reactive({
  visible: false
})

const returnDialog = reactive({
  visible: false
})

const cancelDialog = reactive({
  visible: false
})

const materialDateDialog = reactive({
  visible: false
})

const batchModifyDialog = reactive({
  visible: false
})

const orderSplitDialog = reactive({
  visible: false
})

// 表单数据
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  ddh: null,
  p4: null,
  ORDER_DATE_B: null,
  ORDER_DATE_E: null,
  JH_DATE_B: null,
  JH_DATE_E: null,
  p1: null,
  requireDateFrom: null,
  requireDateTo: null,
  p2: null,
  p8: null,
  createdby: null,
  fhd: null,
  stockStatus: null,
  status: null
})


const returnForm = reactive({
  returnReason: ''
})

const cancelForm = reactive({
  cancelReason: ''
})

const inventoryForm = reactive({
  shipTo: '',
  orderNo: '',
  sapNo: ''
})

const inventoryList = ref([])

const materialDateForm = reactive({
  materialDate: '',
  id: null
})

const batchModifyForm = reactive({
  replyDate: '',
  cause: '',
  remark: ''
})

const orderSplitForm = reactive({
  orderNo: '',
  orderItem: '',
  produceNo: '',
  qty: 0,
  splitQty: 0
})

const materialDateRules = reactive({
  materialDate: [
    { required: true, message: '请选择原料交期', trigger: 'change' }
  ]
})

// 字典选项
const statusOptions = ref([
  { label: '待评审', value: '1' },
  { label: '待点库', value: '2' },
  { label: '待原料交期回复', value: '3' },
  { label: '待下达主计划', value: '4' },
  { label: '主计划下达', value: '5' }
])

// 状态文本获取
const getStatusText = (status) => {
  const statusMap = {
    '1': '待评审',
    '2': '已点库',
    '3': '已评审',
    '4': '已下计划'
  }
  return statusMap[status] || status
}

// 状态类型获取
const getStatusType = (status) => {
  const typeMap = {
    '1': 'warning',
    '2': 'info', 
    '3': 'success',
    '4': 'primary'
  }
  return typeMap[status] || ''
}

// 计划类型文本获取
const getSupplyTypeText = (supplyType) => {
  const typeMap = {
    '0': '自产',
    '1': '外调',
    '2': '内部调料'
  }
  return typeMap[supplyType] || supplyType
}

// 计划类型颜色获取
const getSupplyTypeColor = (supplyType) => {
  const colorMap = {
    '0': 'success',
    '1': 'warning',
    '2': 'info'
  }
  return colorMap[supplyType] || ''
}

// 库存状态文本获取
const getStockStatusText = (stockStatus) => {
  const statusMap = {
    '0': ' ',
    '1': '成品库存',
    '2': 'BB库存',
    '3': '待生产'
  }
  return statusMap[stockStatus] || stockStatus
}

// 库存状态颜色获取
const getStockStatusColor = (stockStatus) => {
  const colorMap = {
    '0': '',
    '1': 'success',
    '2': 'info',
    '3': 'warning'
  }
  return colorMap[stockStatus] || ''
}

// 物料采购类型文本获取
const getBeskzText = (beskz) => {
  const beskzMap = {
    'E': '自制',
    'F': '外采',
    'X': '混合',
    'D': '定制'
  }
  return beskzMap[beskz] || beskz
}

// 物料采购类型颜色获取
const getBeskzColor = (beskz) => {
  const colorMap = {
    'E': 'success',
    'F': 'warning',
    'X': 'info',
    'D': 'primary'
  }
  return colorMap[beskz] || ''
}

// 方法定义
/** Tab切换处理 */
const handleTabChange = (tabName) => {
  if (tabName === 'orderReview') {
    getList()
  }
}

/** 查询销售订单列表 */
const getList = async () => {
  loading.value = true
  try {
    // 模拟数据
    saleChipList.value = [
      {
        id: 1,
        sync: '1',
        msg: '同步成功',
        supplyType: '0',
        deliveryStatus: '0',
        headRemark: '抬头备注',
        specialRemark: '特殊生产要求备注',
        autoReplyDate: '2024-01-20',
        replyDate: '2024-01-25',
        requireDate: '2024-01-15',
        duedays: '2024-02-01',
        plannedCompletionDate: '2024-01-30',
        qty: 1000,
        bzkc: 100,
        bbStock: 200,
        ktsch: 'K001',
        stockStatus: '1',
        sellTo: '1000001',
        customerType: 'A类客户',
        stockHouse: 'WH01',
        spareMark: '1',
        produceNo: '*********',
        sapNo: 'SAP001',
        beskz: 'E',
        specifications: 'STD',
        accuracy: '1%',
        resistance: '1K',
        orderNo: '*********',
        orderItem: '10',
        status: '1'
      }
    ]
    total.value = 1
  } catch (error) {
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}


/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm('queryFormRef')
  handleQuery()
}


/** 多选框选中数据 */
const handleSelectionChange = (selection) => {
  ids.value = selection.map(item => item.id)
}

/** 传SAP操作 */
const handleSendSap = () => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgError("未选择订单!")
    return
  }
  
  // 检查是否已经同步SAP
  const selectedRows = saleChipList.value.filter(item => ids.value.includes(item.id))
  const hassynced = selectedRows.some(item => item.sapOrderNo)
  
  if (hassynced) {
    proxy?.$modal.msgError("选择订单行包含已经同步SAP订单,请确认！")
    return
  }
  
  proxy?.$modal.msgSuccess("传SAP成功")
  getList()
}

/** 点库操作 */
const handleInventory = () => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgError("请选择销售订单!")
    return
  }
  
  const selectedRows = saleChipList.value.filter(item => ids.value.includes(item.id))
  
  // 检查订单状态
  const invalidOrders = selectedRows.filter(item => {
    return item.orderType === 'ZBL' || !item.sapOrderNo || item.status > 2
  })
  
  if (invalidOrders.length > 0) {
    if (invalidOrders.some(item => item.orderType === 'ZBL')) {
      proxy?.$modal.msgError("选择的订单包含ZBL订单,请确认!")
    } else if (invalidOrders.some(item => !item.sapOrderNo)) {
      proxy?.$modal.msgError("选择的订单未同步SAP,请确认!")
    } else {
      proxy?.$modal.msgError("选择的订单已点库存,请确认!")
    }
    return
  }
  
  proxy?.$modal.msgSuccess("点库成功")
  getList()
}

/** 在制点库操作 */
const handleInProcessInventory = () => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgError("请选择销售订单!")
    return
  }
  
  const selectedRows = saleChipList.value.filter(item => ids.value.includes(item.id))
  
  // 检查ZBL订单
  const hasZBL = selectedRows.some(item => item.orderType === 'ZBL')
  if (hasZBL) {
    proxy?.$modal.msgError("ZBL订单不点库!")
    return
  }
  
  // 检查同步状态
  const notSynced = selectedRows.some(item => !item.sapOrderNo)
  if (notSynced) {
    proxy?.$modal.msgError("选择的订单未同步SAP,请确认!")
    return
  }
  
  proxy?.$modal.msgSuccess("在制点库成功")
  getList()
}

/** 传统订单半成品点库操作 */
const handleSemiProductInventory = () => {
  if (ids.value.length === 0) {
    proxy?.$modal.msgError("请选择销售订单!")
    return
  }
  proxy?.$modal.msgSuccess("半成品点库功能开发中")
}

/** 点库明细操作 */
const handleInventoryDetail = (row) => {
  // 填充点库表单数据
  Object.assign(inventoryForm, {
    shipTo: row.shipTo,
    orderNo: row.orderNo,
    sapNo: row.sapNo
  })
  
  inventoryDialog.visible = true
}

/** 查询库存 */
const queryInventory = async () => {
  try {
    buttonLoading.value = true
    // 模拟数据
    inventoryList.value = [
      {
        cw: 'A-01-01',
        ph: 'PSN001',
        kcwlh: 'SAP001',
        kykc: 1000,
        zysl: 0
      }
    ]
    
    proxy?.$modal.msgSuccess("查询成功")
  } catch (error) {
    proxy?.$modal.msgError("查询失败")
  } finally {
    buttonLoading.value = false
  }
}

/** 保存点库 */
const saveInventory = async () => {
  const selectedRows = inventoryList.value.filter(item => item.zysl > 0)
  
  if (selectedRows.length === 0) {
    proxy?.$modal.msgError("未选择要保存的数据！")
    return
  }
  
  try {
    buttonLoading.value = true
    proxy?.$modal.msgSuccess("保存成功")
    inventoryDialog.visible = false
    await getList()
  } catch (error) {
    proxy?.$modal.msgError("保存失败")
  } finally {
    buttonLoading.value = false
  }
}

/** 无库存操作 */
const noStock = async () => {
  try {
    await proxy?.$modal.confirm('确定提交无库存订单信息?')
    
    buttonLoading.value = true
    proxy?.$modal.msgSuccess("提交成功")
    inventoryDialog.visible = false
    await getList()
  } catch (error) {
    // 用户取消操作
  } finally {
    buttonLoading.value = false
  }
}

/** 修改原料交期操作 */
const handleEditMaterialDate = (row) => {
  materialDateForm.materialDate = row.materialDate || ''
  materialDateForm.id = row.id
  materialDateDialog.visible = true
}

/** 确认修改原料交期 */
const confirmEditMaterialDate = async () => {
  try {
    await proxy?.$refs.materialDateFormRef.validate()
    
    buttonLoading.value = true
    proxy?.$modal.msgSuccess("修改成功")
    materialDateDialog.visible = false
    await getList()
  } catch (error) {
    if (error !== 'cancel') {
      proxy?.$modal.msgError("修改失败")
    }
  } finally {
    buttonLoading.value = false
  }
}

/** 确认批量修改交期 */
const confirmBatchModify = async () => {
  if (!batchModifyForm.replyDate) {
    proxy?.$modal.msgError("时间不能为空")
    return
  }
  
  try {
    buttonLoading.value = true
    proxy?.$modal.msgSuccess("批量修改交期成功")
    batchModifyDialog.visible = false
    await getList()
  } catch (error) {
    proxy?.$modal.msgError("批量修改交期失败")
  } finally {
    buttonLoading.value = false
  }
}

/** 确认订单拆分 */
const confirmOrderSplit = async () => {
  if (!orderSplitForm.splitQty || orderSplitForm.splitQty <= 0) {
    proxy?.$modal.msgError("请输入有效的拆分数量")
    return
  }
  
  if (orderSplitForm.splitQty >= orderSplitForm.qty) {
    proxy?.$modal.msgError("拆分数量不能大于等于原始数量")
    return
  }
  
  try {
    buttonLoading.value = true
    proxy?.$modal.msgSuccess("订单拆分成功")
    orderSplitDialog.visible = false
    await getList()
  } catch (error) {
    proxy?.$modal.msgError("订单拆分失败")
  } finally {
    buttonLoading.value = false
  }
}

// 生命周期
onMounted(() => {
  getList()
})
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.section-title {
  margin: 0 0 16px 0;
  padding: 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 2px solid #e4e7ed;
}

.search-actions {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 16px;
}

.table-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.data-table {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

.inventory-info {
  margin-bottom: 20px;
}

.inventory-actions {
  margin: 20px 0;
  text-align: center;
}

.inventory-table {
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .header-actions {
    flex-wrap: wrap;
  }
  
  .header-actions .el-button {
    margin-bottom: 8px;
  }
}

/* 表格优化 */
.data-table .el-table__header-wrapper {
  background-color: #f5f7fa;
}

.data-table .el-table__row:hover {
  background-color: #f5f7fa;
}

/* 状态标签样式 */
.el-tag {
  border-radius: 4px;
}

/* 对话框样式优化 */
.el-dialog__header {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.el-dialog__body {
  padding: 20px;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 16px;
}

.el-input, .el-select, .el-date-picker {
  width: 100%;
}

/* 描述列表样式 */
.el-descriptions {
  margin-bottom: 20px;
}

/* 数字输入框样式 */
.el-input-number {
  width: 100%;
}
</style>