<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" label-width="100px" class="search-form">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="含税标识" prop="taxMark">
                  <el-select v-model="queryParams.taxMark" placeholder="请选择含税标识" clearable @keyup.enter="handleQuery" style="width: 100%">
                    <el-option label="含税" value="1" />
                    <el-option label="不含税" value="0" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="客户代码" prop="kunnr">
                  <el-input v-model="queryParams.kunnr" placeholder="请输入客户代码" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="品名规格" prop="specsName">
                  <el-input v-model="queryParams.specsName" placeholder="请输入品名规格" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="功率" prop="power">
                  <el-input v-model="queryParams.power" placeholder="请输入功率" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="TCR" prop="tcr">
                  <el-input v-model="queryParams.tcr" placeholder="请输入TCR" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="精度" prop="accuracy">
                  <el-input v-model="queryParams.accuracy" placeholder="请输入精度" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="阻值从" prop="startResistanceValue">
                  <el-input v-model="queryParams.startResistanceValue" placeholder="请输入阻值从" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="阻值到" prop="endResistanceValue">
                  <el-input v-model="queryParams.endResistanceValue" placeholder="请输入阻值到" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="包装类型" prop="wayOfPacking">
                  <el-input v-model="queryParams.wayOfPacking" placeholder="请输入包装类型" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="包装数量" prop="quantityOfPacking">
                  <el-input v-model="queryParams.quantityOfPacking" placeholder="请输入包装数量" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="特性2" prop="characteristic2">
                  <el-input v-model="queryParams.characteristic2" placeholder="请输入特性2" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="起始时间" prop="startDate">
                  <el-date-picker 
                    v-model="queryParams.startDate"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择起始时间"
                    clearable
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="结束时间" prop="endDate">
                  <el-date-picker 
                    v-model="queryParams.endDate"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="请选择结束时间"
                    clearable
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="审核状态" prop="auditStatus">
                  <el-select v-model="queryParams.auditStatus" placeholder="请选择审核状态" clearable style="width: 100%">
                    <el-option label="未审核" value="0" />
                    <el-option label="已审核" value="1" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="客户名称" prop="customerName">
                  <el-input v-model="queryParams.customerName" placeholder="请输入客户名称" clearable @keyup.enter="handleQuery" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="有效期内" prop="validPeriod">
                  <el-checkbox v-model="queryParams.validPeriod" true-label="1" false-label="0"></el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item>
                  <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                  <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['price:chipAudit:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['price:chipAudit:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['price:chipAudit:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['price:chipAudit:import']">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['price:chipAudit:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="chipAuditList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="客户代码" align="center" prop="kunnr" />
        <el-table-column label="客户名称" align="center" prop="customerName" />
        <el-table-column label="含税标识" align="center" prop="taxMark">
          <template #default="scope">
            <span>{{ scope.row.taxMark === '1' ? '含税' : '不含税' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="价格" align="center" prop="price" />
        <el-table-column label="定价数量" align="center" prop="priceQuantity" />
        <el-table-column label="定价单位" align="center" prop="priceUnit" />
        <el-table-column label="货币" align="center" prop="currency" />
        <el-table-column label="起始时间" align="center" prop="startDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" align="center" prop="endDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="起始数量" align="center" prop="startQuantity" />
        <el-table-column label="结束数量" align="center" prop="endQuantity" />
        <el-table-column label="品名规格" align="center" prop="specsName" />
        <el-table-column label="功率" align="center" prop="power" />
        <el-table-column label="TCR" align="center" prop="tcr" />
        <el-table-column label="精度" align="center" prop="accuracy" />
        <el-table-column label="阻值从" align="center" prop="startResistanceValue" />
        <el-table-column label="阻值到" align="center" prop="endResistanceValue" />
        <el-table-column label="包装类型" align="center" prop="wayOfPacking" />
        <el-table-column label="包装数量" align="center" prop="quantityOfPacking" />
        <el-table-column label="特性2" align="center" prop="characteristic2" />
        <el-table-column label="创建人" align="center" prop="createBy" />
        <el-table-column label="创建时间" align="center" prop="createDate" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核日期" align="center" prop="auditTime" width="120">
          <template #default="scope">
            <span>{{ parseTime(scope.row.auditTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核人" align="center" prop="auditBy" />
        <el-table-column label="状态" align="center" prop="auditStatus">
          <template #default="scope">
            <el-tag :type="scope.row.auditStatus === '1' ? 'success' : 'warning'">
              {{ scope.row.auditStatus === '1' ? '已审核' : '未审核' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['price:chipAudit:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['price:chipAudit:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改测试单对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="chipAuditFormRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="客户代码" prop="kunnr">
          <el-input v-model="form.kunnr" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="form.customerName" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="含税标识" prop="taxMark">
          <el-select v-model="form.taxMark" placeholder="请选择含税标识" clearable style="width: 100%">
            <el-option label="含税" value="1" />
            <el-option label="不含税" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input v-model="form.price" placeholder="请输入价格" />
        </el-form-item>
        <el-form-item label="定价数量" prop="priceQuantity">
          <el-input v-model="form.priceQuantity" placeholder="请输入定价数量" />
        </el-form-item>
        <el-form-item label="定价单位" prop="priceUnit">
          <el-input v-model="form.priceUnit" placeholder="请输入定价单位" />
        </el-form-item>
        <el-form-item label="货币" prop="currency">
          <el-input v-model="form.currency" placeholder="请输入货币" />
        </el-form-item>
        <el-form-item label="起始时间" prop="startDate">
          <el-date-picker clearable
            v-model="form.startDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择起始时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker clearable
            v-model="form.endDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择结束时间"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="起始数量" prop="startQuantity">
          <el-input v-model="form.startQuantity" placeholder="请输入起始数量" />
        </el-form-item>
        <el-form-item label="结束数量" prop="endQuantity">
          <el-input v-model="form.endQuantity" placeholder="请输入结束数量" />
        </el-form-item>
        <el-form-item label="品名规格" prop="specsName">
          <el-input v-model="form.specsName" placeholder="请输入品名规格" />
        </el-form-item>
        <el-form-item label="功率" prop="power">
          <el-input v-model="form.power" placeholder="请输入功率" />
        </el-form-item>
        <el-form-item label="TCR" prop="tcr">
          <el-input v-model="form.tcr" placeholder="请输入TCR" />
        </el-form-item>
        <el-form-item label="精度" prop="accuracy">
          <el-input v-model="form.accuracy" placeholder="请输入精度" />
        </el-form-item>
        <el-form-item label="阻值从" prop="startResistanceValue">
          <el-input v-model="form.startResistanceValue" placeholder="请输入阻值从" />
        </el-form-item>
        <el-form-item label="阻值到" prop="endResistanceValue">
          <el-input v-model="form.endResistanceValue" placeholder="请输入阻值到" />
        </el-form-item>
        <el-form-item label="包装类型" prop="wayOfPacking">
          <el-input v-model="form.wayOfPacking" placeholder="请输入包装类型" />
        </el-form-item>
        <el-form-item label="包装数量" prop="quantityOfPacking">
          <el-input v-model="form.quantityOfPacking" placeholder="请输入包装数量" />
        </el-form-item>
        <el-form-item label="特性2" prop="characteristic2">
          <el-input v-model="form.characteristic2" placeholder="请输入特性2" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入Excel对话框 -->
    <el-dialog title="导入数据" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />
              是否更新已经存在的数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" :loading="upload.isUploading" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ChipAudit" lang="ts">
    import {
        addChipAudit,
            ChipAuditForm,
        delChipAudit,
        getChipAudit,
        listChipAudit,
            ChipAuditQuery,
        updateChipAudit,
            ChipAuditVO
    } from '@/api/price/chipAudit';
    import { getToken } from '@/utils/auth';

    // 导入选项类型定义
    interface ImportOption {
      open: boolean;
      title: string;
      isUploading: boolean;
      updateSupport: number;
      headers: { Authorization: string };
      url: string;
    }

    const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const chipAuditList = ref<ChipAuditVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const chipAuditFormRef = ref<ElFormInstance>();
const uploadRef = ref<ElUploadInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

// 导入参数
const upload = reactive<ImportOption>({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/price/chipAudit/importData"
});

const initFormData: ChipAuditForm = {
  id: undefined,
  kunnr: undefined,
  customerName: undefined,
  taxMark: undefined,
  price: undefined,
  priceQuantity: undefined,
  priceUnit: undefined,
  currency: undefined,
  startDate: undefined,
  endDate: undefined,
  startQuantity: undefined,
  endQuantity: undefined,
  specsName: undefined,
  power: undefined,
  tcr: undefined,
  accuracy: undefined,
  startResistanceValue: undefined,
  endResistanceValue: undefined,
  wayOfPacking: undefined,
  quantityOfPacking: undefined,
  characteristic2: undefined,
  createDate: undefined,
  deleteBy: undefined,
  deleteDate: undefined,
  deleteFlag: undefined,
  lastModifiedTime: undefined,
  auditTime: undefined,
  auditBy: undefined,
  deptId: undefined,
  userId: undefined,
  orderNum: undefined,
  testKey: undefined,
  value: undefined,
}
const data = reactive<PageData<ChipAuditForm, ChipAuditQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    taxMark: undefined,
    kunnr: undefined,
    specsName: undefined,
    power: undefined,
    tcr: undefined,
    accuracy: undefined,
    startResistanceValue: undefined,
    endResistanceValue: undefined,
    wayOfPacking: undefined,
    quantityOfPacking: undefined,
    characteristic2: undefined,
    startDate: undefined,
    endDate: undefined,
    startQuantity: undefined,
    endQuantity: undefined,
    price: undefined,
    currency: undefined,
    priceQuantity: undefined,
    priceUnit: undefined,
    createDate: undefined,
    deleteBy: undefined,
    deleteDate: undefined,
    deleteFlag: undefined,
    lastModifiedTime: undefined,
    auditTime: undefined,
    auditBy: undefined,
    deptId: undefined,
    userId: undefined,
    orderNum: undefined,
    testKey: undefined,
    value: undefined,
    auditStatus: undefined,
    validPeriod: undefined,
    customerName: undefined,
    params: {
    }
  },
  rules: {
    kunnr: [
      { required: true, message: "客户代码不能为空", trigger: "blur" }
    ],
    customerName: [
      { required: true, message: "客户名称不能为空", trigger: "blur" }
    ],
    taxMark: [
      { required: true, message: "含税标识不能为空", trigger: "blur" }
    ],
    price: [
      { required: true, message: "价格不能为空", trigger: "blur" }
    ],
    priceQuantity: [
      { required: true, message: "定价数量不能为空", trigger: "blur" }
    ],
    priceUnit: [
      { required: true, message: "定价单位不能为空", trigger: "blur" }
    ],
    currency: [
      { required: true, message: "货币不能为空", trigger: "blur" }
    ],
    startDate: [
      { required: true, message: "起始时间不能为空", trigger: "blur" }
    ],
    endDate: [
      { required: true, message: "结束时间不能为空", trigger: "blur" }
    ],
    startQuantity: [
      { required: true, message: "起始数量不能为空", trigger: "blur" }
    ],
    endQuantity: [
      { required: true, message: "结束数量不能为空", trigger: "blur" }
    ],
    specsName: [
      { required: true, message: "品名规格不能为空", trigger: "blur" }
    ],
    power: [
      { required: true, message: "功率不能为空", trigger: "blur" }
    ],
    tcr: [
      { required: true, message: "TCR不能为空", trigger: "blur" }
    ],
    accuracy: [
      { required: true, message: "精度不能为空", trigger: "blur" }
    ],
    startResistanceValue: [
      { required: true, message: "阻值从不能为空", trigger: "blur" }
    ],
    endResistanceValue: [
      { required: true, message: "阻值到不能为空", trigger: "blur" }
    ],
    wayOfPacking: [
      { required: true, message: "包装类型不能为空", trigger: "blur" }
    ],
    quantityOfPacking: [
      { required: true, message: "包装数量不能为空", trigger: "blur" }
    ],
    characteristic2: [
      { required: true, message: "特性2不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询测试单列表 */
const getList = async () => {
  loading.value = true;
  const res = await listChipAudit(queryParams.value);
  chipAuditList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  chipAuditFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ChipAuditVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加测试单";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ChipAuditVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getChipAudit(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改测试单";
}

/** 提交按钮 */
const submitForm = () => {
  chipAuditFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateChipAudit(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addChipAudit(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ChipAuditVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除测试单编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delChipAudit(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('price/chipAudit/export', {
    ...queryParams.value
  }, `chipAudit_${new Date().getTime()}.xlsx`)
}

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = "芯片审核数据";
  upload.open = true;
}

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('price/chipAudit/importTemplate', {}, `芯片审核数据导入模板_${new Date().getTime()}.xlsx`)
}

/** 文件上传中处理 */
const handleFileUploadProgress = (event: any, file: any, fileList: any) => {
  upload.isUploading = true;
}

/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: any, fileList: any) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value!.clearFiles();
  proxy?.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
}

/** 提交上传文件 */
const submitFileForm = () => {
  uploadRef.value!.submit();
}

onMounted(() => {
  getList();
});
</script>

<style scoped>
/* 查询表单样式 */
.search-form {
  background: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.search-form .el-row {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}

.search-form .el-row:last-child {
  margin-bottom: 0;
}

.search-form .el-col {
  padding-right: 20px;
  display: flex;
  align-items: center;
}

.search-form .el-col:last-child {
  padding-right: 0;
}

/* 栅格布局中的表单项样式 */
.search-form .el-form-item {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  width: 100%;
  height: 36px;
}

.search-form .el-form-item__label {
  width: 80px !important;
  min-width: 80px;
  text-align: right;
  font-weight: 500;
  color: #606266;
  line-height: 32px;
  height: 32px;
  margin-bottom: 0;
  margin-right: 12px;
  flex-shrink: 0;
  white-space: nowrap;
}

.search-form .el-form-item__content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  max-width: none;
}

.search-form .el-input,
.search-form .el-select,
.search-form .el-date-picker {
  width: 100% !important;
  height: 32px;
}

.search-form .el-input__wrapper {
  height: 32px;
  line-height: 32px;
}

.search-form .el-select .el-input__wrapper {
  height: 32px;
}

.search-form .el-date-picker .el-input__wrapper {
  height: 32px;
}

.search-form .el-checkbox {
  margin-top: 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  height: 32px;
  line-height: 32px;
  width: 100%;
}

.search-form .el-checkbox__label {
  line-height: 32px;
}

/* 按钮区域对齐 */
.text-right {
  text-align: right;
}

.text-right .el-form-item {
  margin-bottom: 0;
  justify-content: flex-end;
}

.text-right .el-form-item__content {
  justify-content: flex-end;
  max-width: none;
  width: auto;
}

.text-right .el-button {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
}

/* 对话框表单样式 */
.el-dialog .el-form-item {
  margin-bottom: 18px;
  display: flex;
  align-items: center;
}

.el-dialog .el-form-item__label {
  width: 100px;
  min-width: 100px;
  text-align: right;
  font-weight: 500;
  color: #606266;
  line-height: 32px;
  height: 32px;
  margin-right: 12px;
  flex-shrink: 0;
}

.el-dialog .el-form-item__content {
  width: 200px;
  display: flex;
  justify-content: flex-start;
}

.el-dialog .el-input,
.el-dialog .el-select,
.el-dialog .el-date-picker {
  width: 200px;
}

.el-dialog .el-input__wrapper {
  height: 32px;
}

/* 表格样式优化 */
.el-table .el-table__cell {
  padding: 8px 0;
  text-align: center;
}

.el-table .el-table__header-wrapper .el-table__cell {
  background-color: #f5f7fa;
  font-weight: 500;
}

/* 卡片样式 */
.el-card {
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
}

.el-card__body {
  padding: 20px;
}
</style>
