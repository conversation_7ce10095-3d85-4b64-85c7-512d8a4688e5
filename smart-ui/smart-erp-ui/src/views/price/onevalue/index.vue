<template>
  <div class="app-container">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="100px">
            <el-form-item label="客户代码" prop="kunnr">
              <el-input v-model="queryParams.kunnr" placeholder="请输入客户代码" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户料号" prop="customerPn">
              <el-input v-model="queryParams.customerPn" placeholder="请输入客户料号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="标准料号" prop="testKey">
              <el-input v-model="queryParams.testKey" placeholder="请输入标准料号" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="有效期内" prop="validPeriod">
              <el-checkbox v-model="queryParams.validPeriod" />
            </el-form-item>
            <el-form-item label="起始时间" prop="startDate">
              <el-date-picker clearable
                v-model="queryParams.startDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择起始时间"
              />
            </el-form-item>
            <el-form-item label="结束时间" prop="endDate">
              <el-date-picker clearable
                v-model="queryParams.endDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="请选择结束时间"
              />
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select v-model="queryParams.auditStatus" placeholder="未审核" clearable>
                <el-option label="未审核" value="0" />
                <el-option label="已审核" value="1" />
              </el-select>
            </el-form-item>
            <el-form-item class="search-buttons">
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['price:onevalue:add']">新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['price:onevalue:edit']">修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['price:onevalue:remove']">删除</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['price:onevalue:import']">导入</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['price:onevalue:export']">导出</el-button>
          </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="onevalueList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="ID" align="center" prop="id" v-if="false" />
        <el-table-column label="客户代码" align="center" prop="kunnr" />
        <el-table-column label="客户名称" align="center" prop="customerName" />
        <el-table-column label="含税标识" align="center" prop="taxMark" />
        <el-table-column label="价格" align="center" prop="price" />
        <el-table-column label="定价数量" align="center" prop="priceQuantity" />
        <el-table-column label="定价单位" align="center" prop="priceUnit" />
        <el-table-column label="货币" align="center" prop="currency" />
        <el-table-column label="起始时间" align="center" prop="startDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.startDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="结束时间" align="center" prop="endDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.endDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="起始数量" align="center" prop="startQuantity" />
        <el-table-column label="结束数量" align="center" prop="endQuantity" />
        <el-table-column label="品名规格" align="center" prop="specsName" />
        <el-table-column label="功率" align="center" prop="power" />
        <el-table-column label="TCR" align="center" prop="tcr" />
        <el-table-column label="精度" align="center" prop="accuracy" />
        <el-table-column label="阻值从" align="center" prop="startResistanceValue" />
        <el-table-column label="阻值到" align="center" prop="endResistanceValue" />
        <el-table-column label="包装类型" align="center" prop="wayOfPacking" />
        <el-table-column label="包装数量" align="center" prop="quantityOfPacking" />
        <el-table-column label="特性2" align="center" prop="characteristic2" />
        <el-table-column label="创建人" align="center" prop="createBy" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核日期" align="center" prop="auditDate" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.auditDate, '{y}-{m}-{d}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核人" align="center" prop="auditBy" />
        <el-table-column label="状态" align="center" prop="status" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-tooltip content="修改" placement="top">
              <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['price:onevalue:edit']"></el-button>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['price:onevalue:remove']"></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改单阻报价对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="500px" append-to-body>
      <el-form ref="onevalueFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="客户代码" prop="kunnr">
          <el-input v-model="form.kunnr" placeholder="请输入客户代码" />
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <el-input v-model="form.customerName" placeholder="请输入客户名称" />
        </el-form-item>
        <el-form-item label="含税标识" prop="taxMark">
          <el-input v-model="form.taxMark" placeholder="请输入含税标识" />
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input v-model="form.price" placeholder="请输入价格" />
        </el-form-item>
        <el-form-item label="定价数量" prop="priceQuantity">
          <el-input v-model="form.priceQuantity" placeholder="请输入定价数量" />
        </el-form-item>
        <el-form-item label="定价单位" prop="priceUnit">
          <el-input v-model="form.priceUnit" placeholder="请输入定价单位" />
        </el-form-item>
        <el-form-item label="货币" prop="currency">
          <el-input v-model="form.currency" placeholder="请输入货币" />
        </el-form-item>
        <el-form-item label="起始时间" prop="startDate">
          <el-date-picker clearable
            v-model="form.startDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择起始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endDate">
          <el-date-picker clearable
            v-model="form.endDate"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="起始数量" prop="startQuantity">
          <el-input v-model="form.startQuantity" placeholder="请输入起始数量" />
        </el-form-item>
        <el-form-item label="结束数量" prop="endQuantity">
          <el-input v-model="form.endQuantity" placeholder="请输入结束数量" />
        </el-form-item>
        <el-form-item label="品名规格" prop="specsName">
          <el-input v-model="form.specsName" placeholder="请输入品名规格" />
        </el-form-item>
        <el-form-item label="功率" prop="power">
          <el-input v-model="form.power" placeholder="请输入功率" />
        </el-form-item>
        <el-form-item label="TCR" prop="tcr">
          <el-input v-model="form.tcr" placeholder="请输入TCR" />
        </el-form-item>
        <el-form-item label="精度" prop="accuracy">
          <el-input v-model="form.accuracy" placeholder="请输入精度" />
        </el-form-item>
        <el-form-item label="阻值从" prop="startResistanceValue">
          <el-input v-model="form.startResistanceValue" placeholder="请输入阻值从" />
        </el-form-item>
        <el-form-item label="阻值到" prop="endResistanceValue">
          <el-input v-model="form.endResistanceValue" placeholder="请输入阻值到" />
        </el-form-item>
        <el-form-item label="包装类型" prop="wayOfPacking">
          <el-input v-model="form.wayOfPacking" placeholder="请输入包装类型" />
        </el-form-item>
        <el-form-item label="包装数量" prop="quantityOfPacking">
          <el-input v-model="form.quantityOfPacking" placeholder="请输入包装数量" />
        </el-form-item>
        <el-form-item label="特性2" prop="characteristic2">
          <el-input v-model="form.characteristic2" placeholder="请输入特性2" />
        </el-form-item>
        <el-form-item label="客户料号" prop="customerPn">
          <el-input v-model="form.customerPn" placeholder="请输入客户料号" />
        </el-form-item>
        <el-form-item label="标准料号" prop="testKey">
          <el-input v-model="form.testKey" placeholder="请输入标准料号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />
              是否更新已经存在的单阻报价数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size:12px;vertical-align: baseline;" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
  display: flex;
  align-items: center;
}
.el-form-item__label {
  white-space: nowrap !important;
  width: 100px !important;
  text-align: right !important;
  height: 32px;
  line-height: 32px;
  margin-right: 8px;
}
.el-input, .el-date-picker, .el-select {
  width: 220px !important;
}
.el-input__wrapper {
  height: 32px !important;
}
/* 调整选择器样式 */
.el-select .el-input__wrapper {
  height: 32px !important;
}
/* 调整日期选择器样式 */
.el-date-picker .el-input__wrapper {
  height: 32px !important;
}
/* 调整复选框的样式，使其与文本垂直对齐 */
.el-checkbox {
  margin-top: 0;
  display: flex;
  align-items: center;
  height: 32px; /* 与输入框高度一致 */
}
/* 调整表单布局 */
.el-form {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}
/* 确保所有表单项在同一行上对齐 */
.el-form--inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 18px;
}
/* 搜索按钮样式 - 使其显示在最右侧 */
.search-buttons {
  margin-left: auto !important;
  margin-right: 0 !important;
}
.search-buttons .el-form-item__content {
  display: flex;
  gap: 8px;
}
/* 对话框中的表单样式 */
.el-dialog .el-form-item {
  margin-bottom: 18px;
}
.el-dialog .el-input, 
.el-dialog .el-date-picker {
  width: 100% !important;
}
/* 确保对话框中的表单项对齐 */
.el-dialog .el-form-item__label {
  display: inline-block;
  text-align: right;
  padding-right: 12px;
  vertical-align: middle;
}
/* 确保对话框中的按钮对齐 */
.dialog-footer {
  text-align: right;
  margin-top: 10px;
}
</style>

<script setup name="Onevalue" lang="ts">
    import {
        addOnevalue,
            OnevalueForm,
        getOnevalue,
        listOnevalue,
            OnevalueQuery,
        updateOnevalue,
            OnevalueVO,
        delOnevalue,
        importOnevalue,
        downloadTemplate
    } from '@/api/price/onevalue';
    import { getToken } from '@/utils/auth';
    
// 扩展OnevalueQuery类型，添加validPeriod字段
declare module '@/api/price/onevalue' {
  interface OnevalueQuery {
    validPeriod?: boolean;
    currentDate?: string;
    auditStatus?: string;
  }
}

    const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const onevalueList = ref<OnevalueVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const onevalueFormRef = ref<ElFormInstance>();
const uploadRef = ref<ElUploadInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const upload = reactive<UploadOption>({
  open: false,
  title: "",
  isUploading: false,
  updateSupport: 0,
  headers: { Authorization: "Bearer " + getToken() },
  url: import.meta.env.VITE_APP_BASE_API + "/price/onevalue/importData"
});

const initFormData: OnevalueForm = {
  id: undefined,
  kunnr: undefined,
  customerName: undefined,
  taxMark: undefined,
  price: undefined,
  priceQuantity: undefined,
  priceUnit: undefined,
  currency: undefined,
  startDate: undefined,
  endDate: undefined,
  startQuantity: undefined,
  endQuantity: undefined,
  specsName: undefined,
  power: undefined,
  tcr: undefined,
  accuracy: undefined,
  startResistanceValue: undefined,
  endResistanceValue: undefined,
  wayOfPacking: undefined,
  quantityOfPacking: undefined,
  characteristic2: undefined,
  customerPn: undefined,
  testKey: undefined,
  deleteBy: undefined,
  deleteDate: undefined,
  deleteFlag: undefined,
  lastModifiedTime: undefined,
  auditBy: undefined,
  auditDate: undefined,
  deptId: undefined,
  userId: undefined,
  orderNum: undefined,
  value: undefined,
}
const data = reactive<PageData<OnevalueForm, OnevalueQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    kunnr: undefined,
    customerPn: undefined,
    testKey: undefined,
    startDate: undefined,
    endDate: undefined,
    auditStatus: undefined,
    validPeriod: false,
    params: {
    }
  },
  rules: {
    kunnr: [
      { required: true, message: "客户代码不能为空", trigger: "blur" }
    ],
    taxMark: [
      { required: true, message: "含税标识不能为空", trigger: "blur" }
    ],
    customerPn: [
      { required: true, message: "客户料号不能为空", trigger: "blur" }
    ],
    startDate: [
      { required: true, message: "起始时间不能为空", trigger: "blur" }
    ],
    endDate: [
      { required: true, message: "结束时间不能为空", trigger: "blur" }
    ],
    startQuantity: [
      { required: true, message: "起始数量不能为空", trigger: "blur" }
    ],
    endQuantity: [
      { required: true, message: "结束数量不能为空", trigger: "blur" }
    ],
    price: [
      { required: true, message: "价格不能为空", trigger: "blur" }
    ],
    currency: [
      { required: true, message: "货币不能为空", trigger: "blur" }
    ],
    priceQuantity: [
      { required: true, message: "定价数量不能为空", trigger: "blur" }
    ],
    priceUnit: [
      { required: true, message: "定价单位不能为空", trigger: "blur" }
    ],
    testKey: [
      { required: true, message: "标准料号不能为空", trigger: "blur" }
    ],
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询单阻报价列表 */
const getList = async () => {
  loading.value = true;
  try {
    // 构建查询参数
    const params = { ...queryParams.value };
    
    // 如果勾选了有效期内，添加当前日期作为比较条件
    if (params.validPeriod) {
      const today = new Date().toISOString().split('T')[0]; // 获取当前日期，格式为YYYY-MM-DD
      params.currentDate = today;
    }
    
    // 删除validPeriod字段，因为后端API可能不需要这个字段
    delete params.validPeriod;
    
    const res = await listOnevalue(params);
    onevalueList.value = res.rows || [];
    total.value = res.total || 0;
  } catch (error) {
    console.error('获取单阻报价列表失败:', error);
  } finally {
    loading.value = false;
  }
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  onevalueFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: OnevalueVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  dialog.visible = true;
  dialog.title = "添加单阻报价";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: OnevalueVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getOnevalue(_id);
  Object.assign(form.value, res.data);
  dialog.visible = true;
  dialog.title = "修改单阻报价";
}

/** 提交按钮 */
const submitForm = () => {
  onevalueFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id) {
        await updateOnevalue(form.value).finally(() =>  buttonLoading.value = false);
      } else {
        await addOnevalue(form.value).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: OnevalueVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除单阻报价编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delOnevalue(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('price/onevalue/export', {
    ...queryParams.value
  }, `onevalue_${new Date().getTime()}.xlsx`)
}

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = "单阻报价导入";
  upload.open = true;
}

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('price/onevalue/importTemplate', {}, `onevalue_template_${new Date().getTime()}.xlsx`)
}

/** 文件上传中处理 */
const handleFileUploadProgress = (event: any, file: any, fileList: any) => {
  upload.isUploading = true;
}

/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: any, fileList: any) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.clearFiles();
  proxy?.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true });
  getList();
}

/** 提交上传文件 */
const submitFileForm = () => {
  uploadRef.value?.submit();
}

onMounted(() => {
  getList();
});
</script>
