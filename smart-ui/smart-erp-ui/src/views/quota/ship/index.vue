<template>
  <div class="p-2">
    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
      <div v-show="showSearch" class="mb-[10px]">
        <el-card shadow="hover">
          <el-form ref="queryFormRef" :model="queryParams" :inline="true">
            <el-form-item label="渠道" prop="channel">
              <el-input v-model="queryParams.channel" placeholder="请输入渠道" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="行业" prop="trade">
              <el-input v-model="queryParams.trade" placeholder="请输入行业" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="客户简称" prop="customerAbbre">
              <el-input v-model="queryParams.customerAbbre" placeholder="请输入客户简称" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="业务部" prop="saleDept">
              <el-input v-model="queryParams.saleDept" placeholder="请输入业务部" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="售达方" prop="sellTo">
              <el-input v-model="queryParams.sellTo" placeholder="请输入售达方" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="规格" prop="spec">
              <el-input v-model="queryParams.spec" placeholder="请输入规格" clearable @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="年份" prop="yearRange" style="width: 240px;">
              <el-date-picker 
                v-model="queryParams.yearRange" 
                type="yearrange" 
                range-separator="至" 
                start-placeholder="开始年份" 
                end-placeholder="结束年份"
                clearable 
                style="width: 100%;"
                @change="handleYearRangeChange" />
            </el-form-item>
            <el-form-item label="创建日期" prop="createDateRange" style="width: 240px;">
              <el-date-picker 
                v-model="queryParams.createDateRange" 
                type="daterange" 
                range-separator="至" 
                start-placeholder="创建开始日期" 
                end-placeholder="创建结束日期"
                clearable 
                style="width: 100%;"
                value-format="YYYY-MM-DD"
                @change="handleCreateDateRangeChange" />
            </el-form-item>
            <el-form-item label="创建人" prop="createBy" style="width: 180px;">
              <el-input 
                v-model="queryParams.createBy" 
                placeholder="请输入创建人" 
                clearable 
                style="width: 100%;"
                @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="排除已删除" prop="excludeDeleted">
              <el-checkbox 
                v-model="queryParams.excludeDeleted" 
                @change="handleQuery">
              </el-checkbox>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </transition>

    <el-card shadow="never">
      <template #header>
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['quota:ship:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate()" v-hasPermi="['quota:ship:edit']">修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete()" v-hasPermi="['quota:ship:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="Upload" @click="handleImport" v-hasPermi="['quota:ship:import']">导入</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['quota:ship:export']">导出</el-button>
        </el-col>
          <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>
      </template>

      <el-table v-loading="loading" border :data="shipList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="渠道" align="center" prop="channel" />
        <el-table-column label="行业" align="center" prop="trade" />
        <el-table-column label="品牌" align="center" prop="brand" />
        <el-table-column label="业务部" align="center" prop="saleDept" />
        <el-table-column label="售达方" align="center" prop="sellTo" />
        <el-table-column label="客户简称" align="center" prop="customerAbbre" />
        <el-table-column label="客户等级" align="center" prop="customerLevel" />
        <el-table-column label="规格" align="center" prop="spec" />
        <el-table-column label="精度" align="center" prop="accuracy" />
        <el-table-column label="阻值" align="center" prop="resistance" />
        <el-table-column label="阻段" align="center" prop="resistanceRange" />
        <el-table-column label="年份" align="center" prop="year" />
        <el-table-column label="一月份配额" align="center" prop="janQuota" />
        <el-table-column label="一月份已用量" align="center" prop="janUsed" />
        <el-table-column label="一月份余量" align="center" prop="janSurplus" />
        <el-table-column label="一月份使用率" align="center" prop="janUsedRate" />
        <el-table-column label="二月份配额" align="center" prop="febQuota" />
        <el-table-column label="二月份已用量" align="center" prop="febUsed" />
        <el-table-column label="二月份余量" align="center" prop="febSurplus" />
        <el-table-column label="二月份使用率" align="center" prop="febUsedRate" />
        <el-table-column label="三月份配额" align="center" prop="marQuota" />
        <el-table-column label="三月份已用量" align="center" prop="marUsed" />
        <el-table-column label="三月份余量" align="center" prop="marSurplus" />
        <el-table-column label="三月份使用率" align="center" prop="marUsedRate" />
        <el-table-column label="四月份配额" align="center" prop="aprQuota" />
        <el-table-column label="四月份已用量" align="center" prop="aprUsed" />
        <el-table-column label="四月份余量" align="center" prop="aprSurplus" />
        <el-table-column label="四月份使用率" align="center" prop="aprUsedRate" />
        <el-table-column label="五月份配额" align="center" prop="mayQuota" />
        <el-table-column label="五月份已用量" align="center" prop="mayUsed" />
        <el-table-column label="五月份余量" align="center" prop="maySurplus" />
        <el-table-column label="五月份使用率" align="center" prop="mayUsedRate" />
        <el-table-column label="六月份配额" align="center" prop="juneQuota" />
        <el-table-column label="六月份已用量" align="center" prop="juneUsed" />
        <el-table-column label="六月份余量" align="center" prop="juneSurplus" />
        <el-table-column label="六月份使用率" align="center" prop="juneUsedRate" />
        <el-table-column label="七月份配额" align="center" prop="julyQuota" />
        <el-table-column label="七月份使用量" align="center" prop="julyUsed" />
        <el-table-column label="七月份余量" align="center" prop="julySurplus" />
        <el-table-column label="七月份使用率" align="center" prop="julyUsedRate" />
        <el-table-column label="八月份配额" align="center" prop="augQuota" />
        <el-table-column label="八月份已用量" align="center" prop="augUsed" />
        <el-table-column label="八月份余量" align="center" prop="augSurplus" />
        <el-table-column label="八月份使用率" align="center" prop="augUsedRate" />
        <el-table-column label="九月份配额" align="center" prop="septQuota" />
        <el-table-column label="九月份已用量" align="center" prop="septUsed" />
        <el-table-column label="九月份余量" align="center" prop="septSurplus" />
        <el-table-column label="九月份使用率" align="center" prop="septUsedRate" />
        <el-table-column label="十月份配额" align="center" prop="octQuota" />
        <el-table-column label="十月份已用量" align="center" prop="octUsed" />
        <el-table-column label="十月份余量" align="center" prop="octSurplus" />
        <el-table-column label="十月份使用率" align="center" prop="octUsedRate" />
        <el-table-column label="十一月份配额" align="center" prop="novQuota" />
        <el-table-column label="十一月已用量" align="center" prop="novUsed" />
        <el-table-column label="十一月份余量" align="center" prop="novSurplus" />
        <el-table-column label="十一月份使用率" align="center" prop="novUsedRate" />
        <el-table-column label="十二月配额" align="center" prop="decQuota" />
        <el-table-column label="十二月份已用量" align="center" prop="decUsed" />
        <el-table-column label="十二月份余量" align="center" prop="decSurplus" />
        <el-table-column label="十二月份使用率" align="center" prop="decUsedRate" />
        <el-table-column label="创建人" align="center" prop="createByName" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span v-if="scope.row.createTime">{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
            <span v-else style="color: #999;">暂无数据</span>
          </template>
        </el-table-column>
        <el-table-column label="删除人" align="center" prop="deletePerson" />
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize" @pagination="getList" />
    </el-card>
    <!-- 添加或修改出货配额对话框 -->
    <el-dialog :title="dialog.title" v-model="dialog.visible" width="1200px" append-to-body>
      <el-form ref="shipFormRef" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="渠道:" prop="channel">
              <el-input v-model="form.channel" placeholder="请输入渠道" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="行业:" prop="trade">
              <el-input v-model="form.trade" placeholder="请输入行业" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="品牌:" prop="brand">
              <el-input v-model="form.brand" placeholder="请输入品牌" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="业务部:" prop="saleDept">
              <el-select v-model="form.saleDept" placeholder="请选择业务部" style="width: 100%">
                <el-option label="业务部1" value="业务部1" />
                <el-option label="业务部2" value="业务部2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="售达方:" prop="sellTo">
              <el-input v-model="form.sellTo" placeholder="请输入售达方" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户简称:" prop="customerAbbre">
              <el-input v-model="form.customerAbbre" placeholder="请输入客户简称" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="客户等级:" prop="customerLevel">
              <el-input v-model="form.customerLevel" placeholder="请输入客户等级" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="规格:" prop="spec">
              <el-input v-model="form.spec" placeholder="请输入规格" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="精度:" prop="accuracy">
              <el-input v-model="form.accuracy" placeholder="请输入精度" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="阻值:" prop="resistance">
              <el-input v-model="form.resistance" placeholder="请输入阻值" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="阻段:" prop="resistanceRange">
              <el-input v-model="form.resistanceRange" placeholder="请输入阻段" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="年份:" prop="year">
              <el-date-picker
                v-model="form.year"
                type="year"
                placeholder="请选择年份"
                style="width: 100%"
                value-format="YYYY">
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 月份配额部分 -->
        <!-- 月份配额部分 -->
        <el-divider content-position="left">月份配额设置</el-divider>
        
        <!-- 第一季度 -->
        <el-divider content-position="left" style="font-size: 14px; margin: 10px 0;">第一季度</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="一月份配额:" prop="janQuota">
              <el-input v-model="form.janQuota" placeholder="请输入一月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="一月份已用量:" prop="janUsed">
              <el-input v-model="form.janUsed" placeholder="请输入一月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="一月份余量:" prop="janSurplus">
              <el-input v-model="form.janSurplus" placeholder="请输入一月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="一月份使用率:" prop="janUsedRate">
              <el-input v-model="form.janUsedRate" placeholder="请输入一月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="二月份配额:" prop="febQuota">
              <el-input v-model="form.febQuota" placeholder="请输入二月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="二月份已用量:" prop="febUsed">
              <el-input v-model="form.febUsed" placeholder="请输入二月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="二月份余量:" prop="febSurplus">
              <el-input v-model="form.febSurplus" placeholder="请输入二月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="二月份使用率:" prop="febUsedRate">
              <el-input v-model="form.febUsedRate" placeholder="请输入二月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="三月份配额:" prop="marQuota">
              <el-input v-model="form.marQuota" placeholder="请输入三月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="三月份已用量:" prop="marUsed">
              <el-input v-model="form.marUsed" placeholder="请输入三月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="三月份余量:" prop="marSurplus">
              <el-input v-model="form.marSurplus" placeholder="请输入三月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="三月份使用率:" prop="marUsedRate">
              <el-input v-model="form.marUsedRate" placeholder="请输入三月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 第二季度 -->
        <el-divider content-position="left" style="font-size: 14px; margin: 10px 0;">第二季度</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="四月份配额:" prop="aprQuota">
              <el-input v-model="form.aprQuota" placeholder="请输入四月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="四月份已用量:" prop="aprUsed">
              <el-input v-model="form.aprUsed" placeholder="请输入四月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="四月份余量:" prop="aprSurplus">
              <el-input v-model="form.aprSurplus" placeholder="请输入四月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="四月份使用率:" prop="aprUsedRate">
              <el-input v-model="form.aprUsedRate" placeholder="请输入四月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="五月份配额:" prop="mayQuota">
              <el-input v-model="form.mayQuota" placeholder="请输入五月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="五月份已用量:" prop="mayUsed">
              <el-input v-model="form.mayUsed" placeholder="请输入五月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="五月份余量:" prop="maySurplus">
              <el-input v-model="form.maySurplus" placeholder="请输入五月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="五月份使用率:" prop="mayUsedRate">
              <el-input v-model="form.mayUsedRate" placeholder="请输入五月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="六月份配额:" prop="juneQuota">
              <el-input v-model="form.juneQuota" placeholder="请输入六月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="六月份已用量:" prop="juneUsed">
              <el-input v-model="form.juneUsed" placeholder="请输入六月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="六月份余量:" prop="juneSurplus">
              <el-input v-model="form.juneSurplus" placeholder="请输入六月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="六月份使用率:" prop="juneUsedRate">
              <el-input v-model="form.juneUsedRate" placeholder="请输入六月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 第三季度 -->
        <el-divider content-position="left" style="font-size: 14px; margin: 10px 0;">第三季度</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="七月份配额:" prop="julyQuota">
              <el-input v-model="form.julyQuota" placeholder="请输入七月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="七月份已用量:" prop="julyUsed">
              <el-input v-model="form.julyUsed" placeholder="请输入七月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="七月份余量:" prop="julySurplus">
              <el-input v-model="form.julySurplus" placeholder="请输入七月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="七月份使用率:" prop="julyUsedRate">
              <el-input v-model="form.julyUsedRate" placeholder="请输入七月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="八月份配额:" prop="augQuota">
              <el-input v-model="form.augQuota" placeholder="请输入八月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="八月份已用量:" prop="augUsed">
              <el-input v-model="form.augUsed" placeholder="请输入八月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="八月份余量:" prop="augSurplus">
              <el-input v-model="form.augSurplus" placeholder="请输入八月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="八月份使用率:" prop="augUsedRate">
              <el-input v-model="form.augUsedRate" placeholder="请输入八月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="九月份配额:" prop="septQuota">
              <el-input v-model="form.septQuota" placeholder="请输入九月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="九月份已用量:" prop="septUsed">
              <el-input v-model="form.septUsed" placeholder="请输入九月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="九月份余量:" prop="septSurplus">
              <el-input v-model="form.septSurplus" placeholder="请输入九月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="九月份使用率:" prop="septUsedRate">
              <el-input v-model="form.septUsedRate" placeholder="请输入九月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <!-- 第四季度 -->
        <el-divider content-position="left" style="font-size: 14px; margin: 10px 0;">第四季度</el-divider>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="十月份配额:" prop="octQuota">
              <el-input v-model="form.octQuota" placeholder="请输入十月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="十月份已用量:" prop="octUsed">
              <el-input v-model="form.octUsed" placeholder="请输入十月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="十月份余量:" prop="octSurplus">
              <el-input v-model="form.octSurplus" placeholder="请输入十月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="十月份使用率:" prop="octUsedRate">
              <el-input v-model="form.octUsedRate" placeholder="请输入十月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="十一月份配额:" prop="novQuota">
              <el-input v-model="form.novQuota" placeholder="请输入十一月份配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="十一月已用量:" prop="novUsed">
              <el-input v-model="form.novUsed" placeholder="请输入十一月已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="十一月份余量:" prop="novSurplus">
              <el-input v-model="form.novSurplus" placeholder="请输入十一月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="十一月份使用率:" prop="novUsedRate">
              <el-input v-model="form.novUsedRate" placeholder="请输入十一月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="十二月配额:" prop="decQuota">
              <el-input v-model="form.decQuota" placeholder="请输入十二月配额" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="十二月份已用量:" prop="decUsed">
              <el-input v-model="form.decUsed" placeholder="请输入十二月份已用量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="十二月份余量:" prop="decSurplus">
              <el-input v-model="form.decSurplus" placeholder="请输入十二月份余量" />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="十二月份使用率:" prop="decUsedRate">
              <el-input v-model="form.decUsedRate" placeholder="请输入十二月份使用率" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="创建人:" prop="createBy">
              <el-input v-model="form.createBy" placeholder="admin" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="创建时间:" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                type="date"
                placeholder="2025-07-24"
                style="width: 100%"
                value-format="YYYY-MM-DD"
                readonly>
              </el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 出货配额导入对话框 -->
    <el-dialog v-model="upload.open" :title="upload.title" width="400px" append-to-body>
      <el-upload
        ref="uploadRef"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <el-icon class="el-icon--upload">
          <i-ep-upload-filled />
        </el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="text-center el-upload__tip">
            <div class="el-upload__tip"><el-checkbox v-model="upload.updateSupport" />是否更新已经存在的出货配额数据</div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline" @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitFileForm">确 定</el-button>
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Ship" lang="ts">
import { getCurrentInstance, reactive, ref, toRefs, onMounted } from 'vue';
import type { ComponentInternalInstance } from 'vue';
import type { ElFormInstance } from 'element-plus';
import {
    addShip,
    delShip,
    ShipForm,
    getShip,
    listShip,
    ShipQuery,
    updateShip,
    ShipVO,
    downloadShipTemplate
} from '@/api/quota/ship';
import { globalHeaders } from '@/utils/request';
import { to } from 'await-to-js';
import { useUserStore } from '@/store/modules/user';
import { parseTime } from '@/utils/mh';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userStore = useUserStore();

const shipList = ref<ShipVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

/*** 出货配额导入参数 */
const upload = reactive<ImportOption>({
  // 是否显示弹出层（出货配额导入）
  open: false,
  // 弹出层标题（出货配额导入）
  title: '',
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的出货配额数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: globalHeaders(),
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + '/quota/ship/importData'
});

const queryFormRef = ref<ElFormInstance>();
const shipFormRef = ref<ElFormInstance>();
const uploadRef = ref<ElUploadInstance>();

const dialog = reactive<DialogOption>({
  visible: false,
  title: ''
});

const initFormData: ShipForm = {
  id: undefined,
  channel: undefined,
  trade: undefined,
  brand: undefined,
  saleDept: undefined,
  sellTo: '',
  customerAbbre: undefined,
  customerLevel: undefined,
  spec: undefined,
  accuracy: undefined,
  resistance: undefined,
  resistanceRange: undefined,
  deletePerson: undefined,
  lastModifiedTime: undefined,
  deptId: undefined,
  userId: undefined,
  orderNum: undefined,
  year: undefined,
  janQuota: undefined,
  janUsed: undefined,
  janSurplus: undefined,
  janUsedRate: undefined,
  febQuota: undefined,
  febUsed: undefined,
  febSurplus: undefined,
  febUsedRate: undefined,
  marQuota: undefined,
  marUsed: undefined,
  marSurplus: undefined,
  marUsedRate: undefined,
  aprQuota: undefined,
  aprUsed: undefined,
  aprSurplus: undefined,
  aprUsedRate: undefined,
  mayQuota: undefined,
  mayUsed: undefined,
  maySurplus: undefined,
  mayUsedRate: undefined,
  juneQuota: undefined,
  juneUsed: undefined,
  juneSurplus: undefined,
  juneUsedRate: undefined,
  julyQuota: undefined,
  julyUsed: undefined,
  julySurplus: undefined,
  julyUsedRate: undefined,
  augQuota: undefined,
  augUsed: undefined,
  augSurplus: undefined,
  augUsedRate: undefined,
  septQuota: undefined,
  septUsed: undefined,
  septSurplus: undefined,
  septUsedRate: undefined,
  octQuota: undefined,
  octUsed: undefined,
  octSurplus: undefined,
  octUsedRate: undefined,
  novQuota: undefined,
  novUsed: undefined,
  novSurplus: undefined,
  novUsedRate: undefined,
  decQuota: undefined,
  decUsed: undefined,
  decSurplus: undefined,
  decUsedRate: undefined
}
const data = reactive<PageData<ShipForm, ShipQuery>>({
  form: {...initFormData},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    channel: undefined,
    trade: undefined,
    brand: undefined,
    saleDept: undefined,
    sellTo: '',
    customerAbbre: undefined,
    customerLevel: undefined,
    spec: undefined,
    accuracy: undefined,
    resistance: undefined,
    resistanceRange: undefined,
    deletePerson: undefined,
    lastModifiedTime: undefined,
    deptId: undefined,
    userId: undefined,
    orderNum: undefined,
    yearRange: undefined,
    startYear: undefined,
    endYear: undefined,
    createDateRange: undefined,
    createStartDate: undefined,
    createEndDate: undefined,
    createBy: undefined,
    excludeDeleted: true,
    janQuota: undefined,
    janUsed: undefined,
    janSurplus: undefined,
    janUsedRate: undefined,
    febQuota: undefined,
    febUsed: undefined,
    febSurplus: undefined,
    febUsedRate: undefined,
    marQuota: undefined,
    marUsed: undefined,
    marSurplus: undefined,
    marUsedRate: undefined,
    aprQuota: undefined,
    aprUsed: undefined,
    aprSurplus: undefined,
    aprUsedRate: undefined,
    mayQuota: undefined,
    mayUsed: undefined,
    maySurplus: undefined,
    mayUsedRate: undefined,
    juneQuota: undefined,
    juneUsed: undefined,
    juneSurplus: undefined,
    juneUsedRate: undefined,
    julyQuota: undefined,
    julyUsed: undefined,
    julySurplus: undefined,
    julyUsedRate: undefined,
    augQuota: undefined,
    augUsed: undefined,
    augSurplus: undefined,
    augUsedRate: undefined,
    septQuota: undefined,
    septUsed: undefined,
    septSurplus: undefined,
    septUsedRate: undefined,
    octQuota: undefined,
    octUsed: undefined,
    octSurplus: undefined,
    octUsedRate: undefined,
    novQuota: undefined,
    novUsed: undefined,
    novSurplus: undefined,
    novUsedRate: undefined,
    decQuota: undefined,
    decUsed: undefined,
    decSurplus: undefined,
    decUsedRate: undefined,
    params: {
    }
  },
  rules: {
    channel: [
      { required: true, message: "渠道不能为空", trigger: "blur" }
    ],
    trade: [
      { required: true, message: "行业不能为空", trigger: "blur" }
    ],
    brand: [
      { required: true, message: "品牌不能为空", trigger: "blur" }
    ],
    saleDept: [
      { required: true, message: "业务部不能为空", trigger: "blur" }
    ],
    sellTo: [
      { required: true, message: "售达方不能为空", trigger: "blur" }
    ],
    customerAbbre: [
      { required: true, message: "客户简称不能为空", trigger: "blur" }
    ],
    customerLevel: [
      { required: true, message: "客户等级不能为空", trigger: "blur" }
    ],
    accuracy: [
      { required: true, message: "精度不能为空", trigger: "blur" }
    ],
    resistance: [
      { required: true, message: "阻值不能为空", trigger: "blur" }
    ],
    resistanceRange: [
      { required: true, message: "阻段不能为空", trigger: "blur" }
    ],
    year: [
      { required: true, message: "年份不能为空", trigger: "blur" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询出货配额
列表 */
const getList = async () => {
  loading.value = true;
  const res = await listShip(queryParams.value);
  shipList.value = res.rows;
  total.value = res.total;
  loading.value = false;
}

/** 取消按钮 */
const cancel = () => {
  reset();
  dialog.visible = false;
}

/** 表单重置 */
const reset = () => {
  form.value = {...initFormData};
  shipFormRef.value?.resetFields();
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.value.pageNum = 1;
  getList();
}

/** 年份范围变化处理 */
const handleYearRangeChange = (value: any) => {
  if (value && value.length === 2) {
    queryParams.value.startYear = new Date(value[0]).getFullYear();
    queryParams.value.endYear = new Date(value[1]).getFullYear();
  } else {
    queryParams.value.startYear = undefined;
    queryParams.value.endYear = undefined;
  }
  handleQuery();
}

/** 创建日期范围变化处理 */
const handleCreateDateRangeChange = (value: any) => {
  if (value && value.length === 2) {
    queryParams.value.createStartDate = value[0];
    queryParams.value.createEndDate = value[1];
  } else {
    queryParams.value.createStartDate = undefined;
    queryParams.value.createEndDate = undefined;
  }
  handleQuery();
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields();
  handleQuery();
}

/** 多选框选中数据 */
const handleSelectionChange = (selection: ShipVO[]) => {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
const handleAdd = () => {
  reset();
  // 设置业务必需的默认值
  form.value.year = new Date().getFullYear(); // 设置当前年份
  // 设置当前登录用户信息
  form.value.userId = userStore.userId; // 设置当前用户ID
  form.value.deptId = userStore.deptId; // 设置当前部门ID
  dialog.visible = true;
  dialog.title = "添加出货配额";
}

/** 修改按钮操作 */
const handleUpdate = async (row?: ShipVO) => {
  reset();
  const _id = row?.id || ids.value[0]
  const res = await getShip(_id);
  Object.assign(form.value, res.data);
  
  // 确保用户ID和部门ID存在，如果后端返回的数据中没有，则使用当前登录用户的信息
  if (!form.value.userId) {
    form.value.userId = userStore.userId;
  }
  if (!form.value.deptId) {
    form.value.deptId = userStore.deptId;
  }
  
  dialog.visible = true;
  dialog.title = "修改出货配额";
}

/** 提交按钮 */
const submitForm = () => {
  shipFormRef.value?.validate(async (valid: boolean) => {
    if (valid) {
      buttonLoading.value = true;
      
      // 创建提交数据的副本，移除应该由后端自动填充的字段
      const submitData = { ...form.value };
      
      // 只在新增时移除这些字段，让后端自动填充
      if (!submitData.id) {
        delete submitData.deletePerson;
        delete submitData.lastModifiedTime;
        delete submitData.orderNum; // 新增时删除排序号，让后端自动生成
      }
      
      if (submitData.id) {
        await updateShip(submitData).finally(() =>  buttonLoading.value = false);
      } else {
        await addShip(submitData).finally(() =>  buttonLoading.value = false);
      }
      proxy?.$modal.msgSuccess("操作成功");
      dialog.visible = false;
      await getList();
    }
  });
}

/** 删除按钮操作 */
const handleDelete = async (row?: ShipVO) => {
  const _ids = row?.id || ids.value;
  await proxy?.$modal.confirm('是否确认删除出货配额编号为"' + _ids + '"的数据项？').finally(() => loading.value = false);
  await delShip(_ids);
  proxy?.$modal.msgSuccess("删除成功");
  await getList();
}

/** 导出按钮操作 */
const handleExport = () => {
  proxy?.download('quota/ship/export', {
    ...queryParams.value
  }, `ship_${new Date().getTime()}.xlsx`)
}

/** 导入按钮操作 */
const handleImport = () => {
  upload.title = '出货配额导入';
  upload.open = true;
};

/** 下载模板操作 */
const importTemplate = () => {
  proxy?.download('quota/ship/importTemplate', {}, `ship_template_${new Date().getTime()}.xlsx`);
};

/**文件上传中处理 */
const handleFileUploadProgress = () => {
  upload.isUploading = true;
};

/** 文件上传成功处理 */
const handleFileSuccess = (response: any, file: UploadFile) => {
  upload.open = false;
  upload.isUploading = false;
  uploadRef.value?.handleRemove(file);
  ElMessageBox.alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + '</div>', '导入结果', {
    dangerouslyUseHTMLString: true
  });
  getList();
};

/** 提交上传文件 */
function submitFileForm() {
  uploadRef.value?.submit();
}

onMounted(() => {
  getList();
});
</script>
