import request from '@/utils/request';
import {AxiosPromise} from 'axios';
import {CustomerForm, CustomerQuery, CustomerVO, ReallocateQuotaForm} from '@/api/quota/customer/types';

/**
 * 查询订单配额列表
 * @param query
 * @returns {*}
 */

export const listCustomer = (query?: CustomerQuery): AxiosPromise<CustomerVO[]> => {
  return request({
    url: '/quota/customer/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询订单配额详细
 * @param id
 */
export const getCustomer = (id: string | number): AxiosPromise<CustomerVO> => {
  return request({
    url: '/quota/customer/' + id,
    method: 'get'
  });
};

/**
 * 新增订单配额
 * @param data
 */
export const addCustomer = (data: CustomerForm) => {
  return request({
    url: '/quota/customer',
    method: 'post',
    data: data
  });
};

/**
 * 修改订单配额
 * @param data
 */
export const updateCustomer = (data: CustomerForm) => {
  return request({
    url: '/quota/customer',
    method: 'put',
    data: data
  });
};

/**
 * 删除订单配额
 * @param id
 */
export const delCustomer = (id: string | number | Array<string | number>) => {
  return request({
    url: '/quota/customer/' + id,
    method: 'delete'
  });
};

/**
 * 导入订单配额数据
 * @param file 文件
 * @param updateSupport 是否更新已存在数据
 */
export const importCustomer = (file: File, updateSupport: boolean = false) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('updateSupport', updateSupport.toString());
  return request({
    url: '/quota/customer/importData',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 下载订单配额导入模板
 */
export const downloadCustomerTemplate = () => {
  return request({
    url: '/quota/customer/importTemplate',
    method: 'post',
    responseType: 'blob'
  });
};

/**
 * 重新分配已用量
 * @param data 重新分配数据
 */
export const reallocateCustomerQuota = (data: ReallocateQuotaForm) => {
  return request({
    url: '/quota/customer/reallocate',
    method: 'post',
    data: data
  });
};
