import request from '@/utils/request';
import {AxiosPromise} from 'axios';
import {ShipForm, ShipQuery, ShipVO} from '@/api/';

/**
 * 查询出货配额
列表
 * @param query
 * @returns {*}
 */

export const listShip = (query?: ShipQuery): AxiosPromise<ShipVO[]> => {
  return request({
    url: '/quota/ship/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询出货配额
详细
 * @param id
 */
export const getShip = (id: string | number): AxiosPromise<ShipVO> => {
  return request({
    url: '/quota/ship/' + id,
    method: 'get'
  });
};

/**
 * 新增出货配额

 * @param data
 */
export const addShip = (data: ShipForm) => {
  return request({
    url: '/quota/ship',
    method: 'post',
    data: data
  });
};

/**
 * 修改出货配额

 * @param data
 */
export const updateShip = (data: ShipForm) => {
  return request({
    url: '/quota/ship',
    method: 'put',
    data: data
  });
};

/**
 * 删除出货配额
 * @param id 主键
 */
export const delShip = (id: string | number | Array<string | number>) => {
  return request({
    url: '/quota/ship/' + id,
    method: 'delete'
  });
};

/**
 * 导入出货配额数据
 * @param file 文件
 * @param updateSupport 是否更新已存在数据
 */
export const importShip = (file: File, updateSupport: boolean = false) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('updateSupport', updateSupport.toString());
  return request({
    url: '/quota/ship/importData',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 下载出货配额导入模板
 */
export const downloadShipTemplate = () => {
  return request({
    url: '/quota/ship/importTemplate',
    method: 'get',
    responseType: 'blob'
  });
};
