import request from '@/utils/request';
import {AxiosPromise} from 'axios';
import {SaleChipForm, SaleChipQuery, SaleChipVO} from '@/api/order/saleChip/types';

/**
 * 查询订单下达列表
 * @param query
 * @returns {*}
 */

export const listSaleChip = (query?: SaleChipQuery): AxiosPromise<SaleChipVO[]> => {
  return request({
    url: '/order/saleChip/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询订单下达详细
 * @param id
 */
export const getSaleChip = (id: string | number): AxiosPromise<SaleChipVO> => {
  return request({
    url: '/order/saleChip/' + id,
    method: 'get'
  });
};

/**
 * 新增订单下达
 * @param data
 */
export const addSaleChip = (data: SaleChipForm) => {
  return request({
    url: '/order/saleChip',
    method: 'post',
    data: data
  });
};

/**
 * 修改订单下达
 * @param data
 */
export const updateSaleChip = (data: SaleChipForm) => {
  return request({
    url: '/order/saleChip',
    method: 'put',
    data: data
  });
};

/**
 * 删除订单下达
 * @param id
 */
export const delSaleChip = (id: string | number | Array<string | number>) => {
  return request({
    url: '/order/saleChip/' + id,
    method: 'delete'
  });
};
