export interface SaleChipVO {
  /**
   * 主键
   */
  id: string | number;

  /**
   * PT订单号
   */
  orderNo: string;

  /**
   * PT订单项目,从1开始计数
   */
  orderItem: number;

  /**
   * 售达方
   */
  sellTo: string;

  /**
   * 送达方
   */
  shipTo: string;

  /**
   * 下单数量(KPCS)
   */
  qty: number;

  /**
   * 包装数量
   */
  packQty: number;

  /**
   * 单位
   */
  unit: string;

  /**
   * 单价
   */
  price: number;

  /**
   * 货币
   */
  currency: string;

  /**
   * 金额
   */
  amount: number;

  /**
   * 要求交期
   */
  requireDate: string;

  /**
   * 回复交期
   */
  replyDate: string;

  /**
   * 自动回复交期日期
   */
  autoReplyDate: string;

  /**
   * 待排箱数量
   */
  waitBoxQty: number;

  /**
   * 取消数量
   */
  cancelQty: number;

  /**
   * 扫描数量
   */
  scanQty: number;

  /**
   * 已发货数量
   */
  shipQty: number;

  /**
   * 成品库存
   */
  trStock: number;

  /**
   * 成品生产数量
   */
  trProduceQty: number;

  /**
   * BB库存
   */
  bbStock: number;

  /**
   * 生产料号
   */
  produceNo: string;

  /**
   * 标准料号
   */
  standardNo: string;

  /**
   * sap料号
   */
  sapNo: string;

  /**
   * 客户料号1
   */
  customerMaterial1: string;

  /**
   * 发货地
   */
  deliveryPlace: string;

  /**
   * 订单类型
   */
  orderType: string;

  /**
   * 订单标识，0-按库生产(KSV),1-按单生产(KEV)
   */
  orderMark: number;

  /**
   * 是否从备料单中分批，0-不是分批，1-是分批
   */
  spareMark: number;

  /**
   * 供货类型，0-自产(E)，1-外采(F)
   */
  supplyType: number;

  /**
   * 调料类型，1-成品调料，2-bb调料 3-镀前bb调料
   */
  dispatchType: number;

  /**
   * 调料回复交期
   */
  dispatchReplyDate: string;

  /**
   * 状态(-3退回,-2导入失败,-1删除,0导入成功,1已提交,2已点库存,3已传sap,4已下计划,8已全部扫描,9已全部发货)
   */
  status: number;

  /**
   * 库存状态，1-成品库存，2-BB库存，3-待生产
   */
  stockStatus: number;

  /**
   * 生产计划-订单方式：1挪料，2补排程
   */
  planType: number;

  /**
   * 创建人
   */
  creater: string;

  /**
   * 最后修改时间
   */
  lastModifiedTime: string;

  /**
   * 供应商料号
   */
  vendorMaterial: string;

  /**
   * 供应商代码
   */
  vendorCode: string;

  /**
   * 是否扣减信用额度
   */
  creditDeducted: number;

  /**
   * 交期状态
   */
  deliveryStatus: number;

  /**
   * 业务逻辑操作备注信息
   */
  errormsg: string;

  /**
   * 1：超期占用
   */
  overdue: number;

  /**
   * 回复交期修改原因
   */
  cause: string;

  /**
   * 回复交期修改原因备注
   */
  causeRemark: string;

  /**
   * 特殊生产要求
   */
  specialRemark: string;

  /**
   * 部门id
   */
  deptId: string | number;

  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 排序号
   */
  orderNum: number;

  /**
   * key键
   */
  testKey: string;

  /**
   * 值
   */
  value: string;

}

export interface SaleChipForm extends BaseEntity {
  /**
   * 主键
   */
  id?: string | number;

  /**
   * PT订单号
   */
  orderNo?: string;

  /**
   * PT订单项目,从1开始计数
   */
  orderItem?: number;

  /**
   * 售达方
   */
  sellTo?: string;

  /**
   * 送达方
   */
  shipTo?: string;

  /**
   * 下单数量(KPCS)
   */
  qty?: number;

  /**
   * 包装数量
   */
  packQty?: number;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 单价
   */
  price?: number;

  /**
   * 货币
   */
  currency?: string;

  /**
   * 金额
   */
  amount?: number;

  /**
   * 要求交期
   */
  requireDate?: string;

  /**
   * 回复交期
   */
  replyDate?: string;

  /**
   * 自动回复交期日期
   */
  autoReplyDate?: string;

  /**
   * 待排箱数量
   */
  waitBoxQty?: number;

  /**
   * 取消数量
   */
  cancelQty?: number;

  /**
   * 扫描数量
   */
  scanQty?: number;

  /**
   * 已发货数量
   */
  shipQty?: number;

  /**
   * 成品库存
   */
  trStock?: number;

  /**
   * 成品生产数量
   */
  trProduceQty?: number;

  /**
   * BB库存
   */
  bbStock?: number;

  /**
   * 生产料号
   */
  produceNo?: string;

  /**
   * 标准料号
   */
  standardNo?: string;

  /**
   * sap料号
   */
  sapNo?: string;

  /**
   * 客户料号1
   */
  customerMaterial1?: string;

  /**
   * 发货地
   */
  deliveryPlace?: string;

  /**
   * 订单类型
   */
  orderType?: string;

  /**
   * 订单标识，0-按库生产(KSV),1-按单生产(KEV)
   */
  orderMark?: number;

  /**
   * 是否从备料单中分批，0-不是分批，1-是分批
   */
  spareMark?: number;

  /**
   * 供货类型，0-自产(E)，1-外采(F)
   */
  supplyType?: number;

  /**
   * 调料类型，1-成品调料，2-bb调料 3-镀前bb调料
   */
  dispatchType?: number;

  /**
   * 调料回复交期
   */
  dispatchReplyDate?: string;

  /**
   * 状态(-3退回,-2导入失败,-1删除,0导入成功,1已提交,2已点库存,3已传sap,4已下计划,8已全部扫描,9已全部发货)
   */
  status?: number;

  /**
   * 库存状态，1-成品库存，2-BB库存，3-待生产
   */
  stockStatus?: number;

  /**
   * 生产计划-订单方式：1挪料，2补排程
   */
  planType?: number;

  /**
   * 创建人
   */
  creater?: string;

  /**
   * 最后修改时间
   */
  lastModifiedTime?: string;

  /**
   * 供应商料号
   */
  vendorMaterial?: string;

  /**
   * 供应商代码
   */
  vendorCode?: string;

  /**
   * 是否扣减信用额度
   */
  creditDeducted?: number;

  /**
   * 交期状态
   */
  deliveryStatus?: number;

  /**
   * 业务逻辑操作备注信息
   */
  errormsg?: string;

  /**
   * 1：超期占用
   */
  overdue?: number;

  /**
   * 回复交期修改原因
   */
  cause?: string;

  /**
   * 回复交期修改原因备注
   */
  causeRemark?: string;

  /**
   * 特殊生产要求
   */
  specialRemark?: string;

  /**
   * 部门id
   */
  deptId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 排序号
   */
  orderNum?: number;

  /**
   * key键
   */
  testKey?: string;

  /**
   * 值
   */
  value?: string;

}

export interface SaleChipQuery extends PageQuery {

  /**
   * PT订单号
   */
  orderNo?: string;

  /**
   * PT订单项目,从1开始计数
   */
  orderItem?: number;

  /**
   * 售达方
   */
  sellTo?: string;

  /**
   * 送达方
   */
  shipTo?: string;

  /**
   * 下单数量(KPCS)
   */
  qty?: number;

  /**
   * 包装数量
   */
  packQty?: number;

  /**
   * 单位
   */
  unit?: string;

  /**
   * 单价
   */
  price?: number;

  /**
   * 货币
   */
  currency?: string;

  /**
   * 金额
   */
  amount?: number;

  /**
   * 要求交期
   */
  requireDate?: string;

  /**
   * 回复交期
   */
  replyDate?: string;

  /**
   * 自动回复交期日期
   */
  autoReplyDate?: string;

  /**
   * 待排箱数量
   */
  waitBoxQty?: number;

  /**
   * 取消数量
   */
  cancelQty?: number;

  /**
   * 扫描数量
   */
  scanQty?: number;

  /**
   * 已发货数量
   */
  shipQty?: number;

  /**
   * 成品库存
   */
  trStock?: number;

  /**
   * 成品生产数量
   */
  trProduceQty?: number;

  /**
   * BB库存
   */
  bbStock?: number;

  /**
   * 生产料号
   */
  produceNo?: string;

  /**
   * 标准料号
   */
  standardNo?: string;

  /**
   * sap料号
   */
  sapNo?: string;

  /**
   * 客户料号1
   */
  customerMaterial1?: string;

  /**
   * 发货地
   */
  deliveryPlace?: string;

  /**
   * 订单类型
   */
  orderType?: string;

  /**
   * 订单标识，0-按库生产(KSV),1-按单生产(KEV)
   */
  orderMark?: number;

  /**
   * 是否从备料单中分批，0-不是分批，1-是分批
   */
  spareMark?: number;

  /**
   * 供货类型，0-自产(E)，1-外采(F)
   */
  supplyType?: number;

  /**
   * 调料类型，1-成品调料，2-bb调料 3-镀前bb调料
   */
  dispatchType?: number;

  /**
   * 调料回复交期
   */
  dispatchReplyDate?: string;

  /**
   * 状态(-3退回,-2导入失败,-1删除,0导入成功,1已提交,2已点库存,3已传sap,4已下计划,8已全部扫描,9已全部发货)
   */
  status?: number;

  /**
   * 库存状态，1-成品库存，2-BB库存，3-待生产
   */
  stockStatus?: number;

  /**
   * 生产计划-订单方式：1挪料，2补排程
   */
  planType?: number;

  /**
   * 创建人
   */
  creater?: string;

  /**
   * 最后修改时间
   */
  lastModifiedTime?: string;

  /**
   * 供应商料号
   */
  vendorMaterial?: string;

  /**
   * 供应商代码
   */
  vendorCode?: string;

  /**
   * 是否扣减信用额度
   */
  creditDeducted?: number;

  /**
   * 交期状态
   */
  deliveryStatus?: number;

  /**
   * 业务逻辑操作备注信息
   */
  errormsg?: string;

  /**
   * 1：超期占用
   */
  overdue?: number;

  /**
   * 回复交期修改原因
   */
  cause?: string;

  /**
   * 回复交期修改原因备注
   */
  causeRemark?: string;

  /**
   * 特殊生产要求
   */
  specialRemark?: string;

  /**
   * 部门id
   */
  deptId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 排序号
   */
  orderNum?: number;

  /**
   * key键
   */
  testKey?: string;

  /**
   * 值
   */
  value?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



