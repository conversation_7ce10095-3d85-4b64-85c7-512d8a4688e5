export interface OnevalueVO {
  /**
   *
   */
  id: string | number;

  /**
   * 含税标识
   */
  taxMark: string;

  /**
   * 售达方代码
   */
  kunnr: string;

  /**
   * 客户名称
   */
  customerName: string;

  /**
   * 客户料号
   */
  customerPn: string;

  /**
   * 有效期始
   */
  startDate: string;

  /**
   * 有效期至
   */
  endDate: string;

  /**
   *
   */
  startQuantity: number;

  /**
   *
   */
  endQuantity: number;

  /**
   * 单价
   */
  price: number;

  /**
   * 货币
   */
  currency: string;

  /**
   * 定价数量
   */
  priceQuantity: number;

  /**
   * 定价单位
   */
  priceUnit: string;

  /**
   *
   */
  deleteBy: string;

  /**
   *
   */
  deleteDate: string;

  /**
   *
   */
  deleteFlag: string;

  /**
   *
   */
  lastModifiedTime: string;

  /**
   *
   */
  auditBy: string;

  /**
   *
   */
  auditDate: string;

  /**
   * 部门id
   */
  deptId: string | number;

  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 排序号
   */
  orderNum: number;

  /**
   * key键
   */
  testKey: string;

  /**
   * 值
   */
  value: string;

}

export interface OnevalueForm extends BaseEntity {
  /**
   *
   */
  id?: string | number;

  /**
   * 含税标识
   */
  taxMark?: string;

  /**
   * 售达方代码
   */
  kunnr?: string;

  /**
   * 客户名称
   */
  customerName?: string;

  /**
   * 客户名称
   */
  customerName?: string;

  /**
   * 客户料号
   */
  customerPn?: string;

  /**
   * 有效期始
   */
  startDate?: string;

  /**
   * 有效期至
   */
  endDate?: string;

  /**
   *
   */
  startQuantity?: number;

  /**
   *
   */
  endQuantity?: number;

  /**
   * 单价
   */
  price?: number;

  /**
   * 货币
   */
  currency?: string;

  /**
   * 定价数量
   */
  priceQuantity?: number;

  /**
   * 定价单位
   */
  priceUnit?: string;

  /**
   *
   */
  deleteBy?: string;

  /**
   *
   */
  deleteDate?: string;

  /**
   *
   */
  deleteFlag?: string;

  /**
   *
   */
  lastModifiedTime?: string;

  /**
   *
   */
  auditBy?: string;

  /**
   *
   */
  auditDate?: string;

  /**
   * 部门id
   */
  deptId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 排序号
   */
  orderNum?: number;

  /**
   * key键
   */
  testKey?: string;

  /**
   * 值
   */
  value?: string;

}

export interface OnevalueQuery extends PageQuery {

  /**
   * 含税标识
   */
  taxMark?: string;

  /**
   * 售达方代码
   */
  kunnr?: string;

  /**
   * 客户料号
   */
  customerPn?: string;

  /**
   * 有效期始
   */
  startDate?: string;

  /**
   * 有效期至
   */
  endDate?: string;

  /**
   *
   */
  startQuantity?: number;

  /**
   *
   */
  endQuantity?: number;

  /**
   * 单价
   */
  price?: number;

  /**
   * 货币
   */
  currency?: string;

  /**
   * 定价数量
   */
  priceQuantity?: number;

  /**
   * 定价单位
   */
  priceUnit?: string;

  /**
   *
   */
  deleteBy?: string;

  /**
   *
   */
  deleteDate?: string;

  /**
   *
   */
  deleteFlag?: string;

  /**
   *
   */
  lastModifiedTime?: string;

  /**
   *
   */
  auditBy?: string;

  /**
   *
   */
  auditDate?: string;

  /**
   * 部门id
   */
  deptId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 排序号
   */
  orderNum?: number;

  /**
   * key键
   */
  testKey?: string;

  /**
   * 值
   */
  value?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



