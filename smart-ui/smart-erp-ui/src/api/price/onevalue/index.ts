import request from '@/utils/request';
import {AxiosPromise} from 'axios';
import {OnevalueForm, OnevalueQuery, OnevalueVO} from './types';

/**
 * 查询单阻报价列表
 * @param query
 * @returns {*}
 */

export const listOnevalue = (query?: OnevalueQuery): AxiosPromise<{rows: OnevalueVO[], total: number}> => {
  return request({
    url: '/price/onevalue/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询单阻报价详细
 * @param id
 */
export const getOnevalue = (id: string | number): AxiosPromise<OnevalueVO> => {
  return request({
    url: '/price/onevalue/' + id,
    method: 'get'
  });
};

/**
 * 新增单阻报价
 * @param data
 */
export const addOnevalue = (data: OnevalueForm) => {
  return request({
    url: '/price/onevalue',
    method: 'post',
    data: data
  });
};

/**
 * 修改单阻报价
 * @param data
 */
export const updateOnevalue = (data: OnevalueForm) => {
  return request({
    url: '/price/onevalue',
    method: 'put',
    data: data
  });
};

/**
 * 删除单阻报价
 * @param id
 */
export const delOnevalue = (id: string | number | Array<string | number>) => {
  return request({
    url: '/price/onevalue/' + id,
    method: 'delete'
  });
};

/**
 * 导入单阻报价数据
 * @param data
 */
export const importOnevalue = (data: FormData) => {
  return request({
    url: '/price/onevalue/importData',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

/**
 * 下载单阻报价导入模板
 */
export const downloadTemplate = () => {
  return request({
    url: '/price/onevalue/importTemplate',
    method: 'post',
    responseType: 'blob'
  });
};
