export interface ChipAuditVO {
  /**
   * id
   */
  id: string | number;

  /**
   * 含税标识
   */
  taxMark: string;

  /**
   * 售达方代码
   */
  kunnr: string;

  /**
   * 客户名称
   */
  customerName: string;

  /**
   * 品名规格
   */
  specsName: string;

  /**
   * 功率
   */
  power: string;

  /**
   * 温度系数
   */
  tcr: string;

  /**
   * 精度
   */
  accuracy: string;

  /**
   * 阻值从
   */
  startResistanceValue: number;

  /**
   * 阻值到
   */
  endResistanceValue: number;

  /**
   * 包装类型
   */
  wayOfPacking: string;

  /**
   * 包装数量
   */
  quantityOfPacking: string;

  /**
   * 特性2
   */
  characteristic2: string;

  /**
   * 有效期始
   */
  startDate: string;

  /**
   * 有效期至
   */
  endDate: string;

  /**
   * 开始数量
   */
  startQuantity: number;

  /**
   * 结束数量
   */
  endQuantity: number;

  /**
   * 单价
   */
  price: number;

  /**
   * 货币
   */
  currency: string;

  /**
   * 定价数量
   */
  priceQuantity: number;

  /**
   * 定价单位
   */
  priceUnit: string;

  /**
   * 创建时间
   */
  createDate: string;

  /**
   * 删除人
   */
  deleteBy: string;

  /**
   * 删除时间
   */
  deleteDate: string;

  /**
   * 删除标记
   */
  deleteFlag: string;

  /**
   * 修改时间
   */
  lastModifiedTime: string;

  /**
   * 审核时间
   */
  auditTime: string;

  /**
   * 审核人
   */
  auditBy: string;

  /**
   * 审核状态
   */
  auditStatus: string;

  /**
   * 部门id
   */
  deptId: string | number;

  /**
   * 用户id
   */
  userId: string | number;

  /**
   * 排序号
   */
  orderNum: number;

  /**
   * key键
   */
  testKey: string;

  /**
   * 值
   */
  value: string;

}

export interface ChipAuditForm extends BaseEntity {
  /**
   * id
   */
  id?: string | number;

  /**
   * 含税标识
   */
  taxMark?: string;

  /**
   * 售达方代码
   */
  kunnr?: string;

  /**
   * 客户名称
   */
  customerName?: string;

  /**
   * 品名规格
   */
  specsName?: string;

  /**
   * 功率
   */
  power?: string;

  /**
   * 温度系数
   */
  tcr?: string;

  /**
   * 精度
   */
  accuracy?: string;

  /**
   * 阻值从
   */
  startResistanceValue?: number;

  /**
   * 阻值到
   */
  endResistanceValue?: number;

  /**
   * 包装类型
   */
  wayOfPacking?: string;

  /**
   * 包装数量
   */
  quantityOfPacking?: string;

  /**
   * 特性2
   */
  characteristic2?: string;

  /**
   * 有效期始
   */
  startDate?: string;

  /**
   * 有效期至
   */
  endDate?: string;

  /**
   * 开始数量
   */
  startQuantity?: number;

  /**
   * 结束数量
   */
  endQuantity?: number;

  /**
   * 单价
   */
  price?: number;

  /**
   * 货币
   */
  currency?: string;

  /**
   * 定价数量
   */
  priceQuantity?: number;

  /**
   * 定价单位
   */
  priceUnit?: string;

  /**
   * 创建时间
   */
  createDate?: string;

  /**
   * 删除人
   */
  deleteBy?: string;

  /**
   * 删除时间
   */
  deleteDate?: string;

  /**
   * 删除标记
   */
  deleteFlag?: string;

  /**
   * 修改时间
   */
  lastModifiedTime?: string;

  /**
   * 审核时间
   */
  auditTime?: string;

  /**
   * 审核人
   */
  auditBy?: string;

  /**
   * 审核状态
   */
  auditStatus?: string;

  /**
   * 部门id
   */
  deptId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 排序号
   */
  orderNum?: number;

  /**
   * key键
   */
  testKey?: string;

  /**
   * 值
   */
  value?: string;

}

export interface ChipAuditQuery extends PageQuery {

  /**
   * 含税标识
   */
  taxMark?: string;

  /**
   * 售达方代码
   */
  kunnr?: string;

  /**
   * 客户名称
   */
  customerName?: string;

  /**
   * 品名规格
   */
  specsName?: string;

  /**
   * 功率
   */
  power?: string;

  /**
   * 温度系数
   */
  tcr?: string;

  /**
   * 精度
   */
  accuracy?: string;

  /**
   * 阻值从
   */
  startResistanceValue?: number;

  /**
   * 阻值到
   */
  endResistanceValue?: number;

  /**
   * 包装类型
   */
  wayOfPacking?: string;

  /**
   * 包装数量
   */
  quantityOfPacking?: string;

  /**
   * 特性2
   */
  characteristic2?: string;

  /**
   * 有效期始
   */
  startDate?: string;

  /**
   * 有效期至
   */
  endDate?: string;

  /**
   * 开始数量
   */
  startQuantity?: number;

  /**
   * 结束数量
   */
  endQuantity?: number;

  /**
   * 单价
   */
  price?: number;

  /**
   * 货币
   */
  currency?: string;

  /**
   * 定价数量
   */
  priceQuantity?: number;

  /**
   * 定价单位
   */
  priceUnit?: string;

  /**
   * 创建时间
   */
  createDate?: string;

  /**
   * 删除人
   */
  deleteBy?: string;

  /**
   * 删除时间
   */
  deleteDate?: string;

  /**
   * 删除标记
   */
  deleteFlag?: string;

  /**
   * 修改时间
   */
  lastModifiedTime?: string;

  /**
   * 审核时间
   */
  auditTime?: string;

  /**
   * 审核人
   */
  auditBy?: string;

  /**
   * 审核状态
   */
  auditStatus?: string;

  /**
   * 部门id
   */
  deptId?: string | number;

  /**
   * 用户id
   */
  userId?: string | number;

  /**
   * 排序号
   */
  orderNum?: number;

  /**
   * key键
   */
  testKey?: string;

  /**
   * 值
   */
  value?: string;

    /**
     * 日期范围参数
     */
    params?: any;
}



