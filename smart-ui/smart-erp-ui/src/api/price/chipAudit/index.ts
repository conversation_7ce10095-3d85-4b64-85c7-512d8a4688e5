import request from '@/utils/request';
import {AxiosPromise} from 'axios';
import {ChipAuditForm, ChipAuditQuery, ChipAuditVO} from './types';

/**
 * 查询测试单列表
 * @param query
 * @returns {*}
 */
export const listChipAudit = (query?: ChipAuditQuery): AxiosPromise<ChipAuditVO[]> => {
  return request({
    url: '/price/chipAudit/list',
    method: 'get',
    params: query
  });
};

/**
 * 查询测试单详细
 * @param id
 */
export const getChipAudit = (id: string | number): AxiosPromise<ChipAuditVO> => {
  return request({
    url: '/price/chipAudit/' + id,
    method: 'get'
  });
};

/**
 * 新增测试单
 * @param data
 */
export const addChipAudit = (data: ChipAuditForm) => {
  return request({
    url: '/price/chipAudit',
    method: 'post',
    data: data
  });
};

/**
 * 修改测试单
 * @param data
 */
export const updateChipAudit = (data: ChipAuditForm) => {
  return request({
    url: '/price/chipAudit',
    method: 'put',
    data: data
  });
};

/**
 * 删除测试单
 * @param id
 */
export const delChipAudit = (id: string | number | Array<string | number>) => {
  return request({
    url: '/price/chipAudit/' + id,
    method: 'delete'
  });
};

/**
 * 导入芯片审核数据
 * @param data
 */
export const importChipAudit = (data: any) => {
  return request({
    url: '/price/chipAudit/import',
    method: 'post',
    data: data
  });
};

/**
 * 下载模板
 */
export const downloadTemplate = () => {
  return request({
    url: '/price/chipAudit/downloadTemplate',
    method: 'get',
    responseType: 'blob'
  });
};
