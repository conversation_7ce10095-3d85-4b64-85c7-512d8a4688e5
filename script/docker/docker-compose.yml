services:
  mysql:
    image: mysql:8.0.42
    container_name: mysql
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      # root 密码
      MYSQL_ROOT_PASSWORD: MH@2025$platform
      # 初始化数据库(后续的初始化sql会在这个库执行)
      MYSQL_DATABASE: smart-erp
    ports:
      - "3306:3306"
    volumes:
      # 数据挂载
      - /docker/mysql/data/:/var/lib/mysql/
      # 配置挂载
      - /docker/mysql/conf/:/etc/mysql/conf.d/
    command:
      # 将mysql8.0默认密码策略 修改为 原先 策略 (mysql8.0对其默认策略做了更改 会导致密码无法匹配)
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
    privileged: true
    network_mode: "host"

  nginx-web:
    image: nginx:1.23.4
    container_name: nginx-web
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 证书映射
      - /docker/nginx/cert:/etc/nginx/cert
      # 配置文件映射
      - /docker/nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      # 页面目录
      - /docker/nginx/html:/usr/share/nginx/html
      # 日志目录
      - /docker/nginx/log:/var/log/nginx
    privileged: true
    network_mode: "host"

  redis:
    image: redis:7.2.8
    container_name: redis
    ports:
      - "6379:6379"
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    volumes:
      # 配置文件
      - /docker/redis/conf:/redis/config:rw
      # 数据文件
      - /docker/redis/data/:/redis/data/:rw
    command: "redis-server /redis/config/redis.conf"
    privileged: true
    network_mode: "host"

  minio:
    # minio 最后一个未阉割版本 不能再进行升级 在往上的版本功能被阉割
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z
    container_name: minio
    ports:
      # api 端口
      - "9000:9000"
      # 控制台端口
      - "9001:9001"
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      # 管理后台用户名
      MINIO_ROOT_USER: mh
      # 管理后台密码，最小8个字符
      MINIO_ROOT_PASSWORD: mh123
      # https需要指定域名
      #MINIO_SERVER_URL: "https://xxx.com:9000"
      #MINIO_BROWSER_REDIRECT_URL: "https://xxx.com:9001"
      # 开启压缩 on 开启 off 关闭
      MINIO_COMPRESS: "off"
      # 扩展名 .pdf,.doc 为空 所有类型均压缩
      MINIO_COMPRESS_EXTENSIONS: ""
      # mime 类型 application/pdf 为空 所有类型均压缩
      MINIO_COMPRESS_MIME_TYPES: ""
    volumes:
      # 映射当前目录下的data目录至容器内/data目录
      - /docker/minio/data:/data
      # 映射配置目录
      - /docker/minio/config:/root/.minio/
    command: server --address ':9000' --console-address ':9001' /data  # 指定容器中的目录 /data
    privileged: true
    network_mode: "host"

  mh-server1:
    image: mh/mh-server:5.4.1
    container_name: mh-server1
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      SERVER_PORT: 8080
      SNAIL_PORT: 28080
    volumes:
      # 配置文件
      - /docker/server1/logs/:/mh/server/logs/
      # skywalking 探针
#      - /docker/skywalking/agent/:/mh/skywalking/agent
    privileged: true
    network_mode: "host"

  mh-server2:
    image: mh/mh-server:5.4.1
    container_name: mh-server2
    environment:
      # 时区上海
      TZ: Asia/Shanghai
      SERVER_PORT: 8081
      SNAIL_PORT: 28081
    volumes:
      # 配置文件
      - /docker/server2/logs/:/mh/server/logs/
      # skywalking 探针
#      - /docker/skywalking/agent/:/mh/skywalking/agent
    privileged: true
    network_mode: "host"

  mh-monitor-admin:
    image: mh/mh-monitor-admin:5.4.1
    container_name: mh-monitor-admin
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    volumes:
      # 配置文件
      - /docker/monitor/logs/:/mh/monitor/logs
    privileged: true
    network_mode: "host"

  mh-snailjob-server:
    image: mh/mh-snailjob-server:5.4.1
    container_name: mh-snailjob-server
    environment:
      # 时区上海
      TZ: Asia/Shanghai
    ports:
      - "8800:8800"
      - "17888:17888"
    volumes:
      - /docker/snailjob/logs/:/mh/snailjob/logs
    privileged: true
    network_mode: "host"
