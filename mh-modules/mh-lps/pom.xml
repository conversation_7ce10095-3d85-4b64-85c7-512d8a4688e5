<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.mh</groupId>
        <artifactId>mh-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mh-lps</artifactId>

    <description>
        lps模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-excel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.mh</groupId>
            <artifactId>mh-common-idempotent</artifactId>
        </dependency>

    </dependencies>

</project>
