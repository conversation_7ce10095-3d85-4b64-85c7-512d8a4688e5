package com.mh.lps.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.mh.common.excel.annotation.ExcelDictFormat;
import com.mh.lps.domain.LpsRepairRequests;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 报修请求视图对象 lps_repair_requests
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LpsRepairRequests.class)
public class LpsRepairRequestsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报修请求ID
     */
    @ExcelProperty(value = "报修请求ID")
    private Long id;

    /**
     * 报修单号，唯一
     */
    @ExcelProperty(value = "报修单号，唯一")
    private String requestSn;

    /**
     * 机台ID，关联lps_machines表
     */
    @ExcelProperty(value = "机台ID，关联lps_machines表")
    private Long machineId;

    /**
     * 报修人ID，此处假设用户ID由外部系统提供或在应用层面管理
     */
    @ExcelProperty(value = "报修人ID，此处假设用户ID由外部系统提供或在应用层面管理")
    private Long requesterId;

    /**
     * 报修时间
     */
    @ExcelProperty(value = "报修时间")
    private Date requestTime;

    /**
     * 报修状态：pending(待维修), in_progress(维修中), completed(已完成), cancelled(已取消)
     */
    @ExcelProperty(value = "报修状态：pending(待维修), in_progress(维修中), completed(已完成), cancelled(已取消)")
    private String status;

    /**
     * 维修技术人员ID，此处假设用户ID由外部系统提供或在应用层面管理
     */
    @ExcelProperty(value = "维修技术人员ID，此处假设用户ID由外部系统提供或在应用层面管理")
    private Long repairerId;

    /**
     * 技术人员接单时间
     */
    @ExcelProperty(value = "技术人员接单时间")
    private Date acceptTime;

    /**
     * 维修完成时间
     */
    @ExcelProperty(value = "维修完成时间")
    private Date completeTime;

    /**
     * 故障描述
     */
    @ExcelProperty(value = "故障描述")
    private String description;

    /**
     * 记录创建时间
     */
    @ExcelProperty(value = "记录创建时间")
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    @ExcelProperty(value = "记录最后更新时间")
    private Date updatedAt;


}
