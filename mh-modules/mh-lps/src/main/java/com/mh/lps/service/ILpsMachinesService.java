package com.mh.lps.service;

import com.mh.common.mybatis.core.page.TableDataInfo;
import com.mh.common.mybatis.core.page.PageQuery;
import com.mh.lps.domain.bo.LpsMachinesBo;
import com.mh.lps.domain.vo.LpsMachinesVo;

import java.util.Collection;
import java.util.List;

/**
 * 机台基础信息Service接口
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
public interface ILpsMachinesService {

    /**
     * 查询机台基础信息
     *
     * @param id 主键
     * @return 机台基础信息
     */
    LpsMachinesVo queryById(Long id);

    /**
     * 分页查询机台基础信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 机台基础信息分页列表
     */
    TableDataInfo<LpsMachinesVo> queryPageList(LpsMachinesBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的机台基础信息列表
     *
     * @param bo 查询条件
     * @return 机台基础信息列表
     */
    List<LpsMachinesVo> queryList(LpsMachinesBo bo);

    /**
     * 新增机台基础信息
     *
     * @param bo 机台基础信息
     * @return 是否新增成功
     */
    Boolean insertByBo(LpsMachinesBo bo);

    /**
     * 修改机台基础信息
     *
     * @param bo 机台基础信息
     * @return 是否修改成功
     */
    Boolean updateByBo(LpsMachinesBo bo);

    /**
     * 校验并批量删除机台基础信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
