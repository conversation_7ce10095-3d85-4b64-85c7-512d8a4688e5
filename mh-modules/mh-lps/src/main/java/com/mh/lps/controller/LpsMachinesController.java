package com.mh.lps.controller;

import java.util.List;

import com.mh.common.idempotent.annotation.RepeatSubmit;
import com.mh.lps.domain.bo.LpsMachinesBo;
import com.mh.lps.domain.vo.LpsMachinesVo;
import com.mh.lps.service.ILpsMachinesService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.mh.common.log.annotation.Log;
import com.mh.common.web.core.BaseController;
import com.mh.common.mybatis.core.page.PageQuery;
import com.mh.common.core.domain.R;
import com.mh.common.core.validate.AddGroup;
import com.mh.common.core.validate.EditGroup;
import com.mh.common.log.enums.BusinessType;
import com.mh.common.excel.utils.ExcelUtil;
import com.mh.common.mybatis.core.page.TableDataInfo;

/**
 * 机台基础信息
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/lps/machines")
public class LpsMachinesController extends BaseController {

    private final ILpsMachinesService lpsMachinesService;

    /**
     * 查询机台基础信息列表
     */
    @SaCheckPermission("system:machines:list")
    @GetMapping("/list")
    public TableDataInfo<LpsMachinesVo> list(LpsMachinesBo bo, PageQuery pageQuery) {
        return lpsMachinesService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出机台基础信息列表
     */
    @SaCheckPermission("system:machines:export")
    @Log(title = "机台基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LpsMachinesBo bo, HttpServletResponse response) {
        List<LpsMachinesVo> list = lpsMachinesService.queryList(bo);
        ExcelUtil.exportExcel(list, "机台基础信息", LpsMachinesVo.class, response);
    }

    /**
     * 获取机台基础信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:machines:query")
    @GetMapping("/{id}")
    public R<LpsMachinesVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(lpsMachinesService.queryById(id));
    }

    /**
     * 新增机台基础信息
     */
    @SaCheckPermission("system:machines:add")
    @Log(title = "机台基础信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LpsMachinesBo bo) {
        return toAjax(lpsMachinesService.insertByBo(bo));
    }

    /**
     * 修改机台基础信息
     */
    @SaCheckPermission("system:machines:edit")
    @Log(title = "机台基础信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LpsMachinesBo bo) {
        return toAjax(lpsMachinesService.updateByBo(bo));
    }

    /**
     * 删除机台基础信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:machines:remove")
    @Log(title = "机台基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(lpsMachinesService.deleteWithValidByIds(List.of(ids), true));
    }
}
