package com.mh.lps.domain.bo;

import com.mh.common.mybatis.core.domain.BaseEntity;
import com.mh.lps.domain.LpsParts;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import com.mh.common.core.validate.AddGroup;
import com.mh.common.core.validate.EditGroup;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 零配件基础信息业务对象 lps_parts
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LpsParts.class, reverseConvertGenerate = false)
public class LpsPartsBo extends BaseEntity {

    /**
     * 零配件ID
     */
    private Long id;

    /**
     * 零配件名称
     */
    private String partName;

    /**
     * 零配件型号
     */
    @NotBlank(message = "零配件型号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String partModel;

    /**
     * 零配件编码，唯一
     */
    @NotBlank(message = "零配件编码，唯一不能为空", groups = { AddGroup.class, EditGroup.class })
    private String partCode;

    /**
     * 计量单位（如：个、米、套）
     */
    @NotBlank(message = "计量单位（如：个、米、套）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String unit;

    /**
     * 零配件描述
     */
    @NotBlank(message = "零配件描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 当前库存量
     */
    private Long currentStock;

    /**
     * 最低库存预警值
     */
    @NotNull(message = "最低库存预警值不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long minStockAlert;

    /**
     * 存放位置
     */
    @NotBlank(message = "存放位置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String location;

    /**
     * 记录创建时间
     */
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    private Date updatedAt;


}
