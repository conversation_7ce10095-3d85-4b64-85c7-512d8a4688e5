package com.mh.lps.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.mh.common.excel.annotation.ExcelDictFormat;
import com.mh.lps.domain.LpsMachines;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 机台基础信息视图对象 lps_machines
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LpsMachines.class)
public class LpsMachinesVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 机台ID
     */
    @ExcelProperty(value = "机台ID")
    private Long id;

    /**
     * 机台编号，唯一
     */
    @ExcelProperty(value = "机台编号，唯一")
    private String machineSn;

    /**
     * 机台名称
     */
    @ExcelProperty(value = "机台名称")
    private String machineName;

    /**
     * 所在产线/位置
     */
    @ExcelProperty(value = "所在产线/位置")
    private String location;

    /**
     * 机台二维码URL
     */
    @ExcelProperty(value = "机台二维码URL")
    private String qrCodeUrl;

    /**
     * 机台状态：active(正常), in_repair(维修中), out_of_service(停用)
     */
    @ExcelProperty(value = "机台状态：active(正常), in_repair(维修中), out_of_service(停用)")
    private String status;

    /**
     * 记录创建时间
     */
    @ExcelProperty(value = "记录创建时间")
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    @ExcelProperty(value = "记录最后更新时间")
    private Date updatedAt;


}
