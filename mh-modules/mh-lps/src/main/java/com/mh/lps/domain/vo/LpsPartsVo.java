package com.mh.lps.domain.vo;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import com.mh.common.excel.annotation.ExcelDictFormat;
import com.mh.common.excel.convert.ExcelDictConvert;
import com.mh.lps.domain.LpsParts;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 零配件基础信息视图对象 lps_parts
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = LpsParts.class)
public class LpsPartsVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 零配件ID
     */
    @ExcelProperty(value = "零配件ID")
    private Long id;

    /**
     * 零配件名称
     */
    @ExcelProperty(value = "零配件名称")
    private String partName;

    /**
     * 零配件型号
     */
    @ExcelProperty(value = "零配件型号")
    private String partModel;

    /**
     * 零配件编码，唯一
     */
    @ExcelProperty(value = "零配件编码，唯一")
    private String partCode;

    /**
     * 计量单位（如：个、米、套）
     */
    @ExcelProperty(value = "计量单位", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "如=：个、米、套")
    private String unit;

    /**
     * 零配件描述
     */
    @ExcelProperty(value = "零配件描述")
    private String description;

    /**
     * 当前库存量
     */
    @ExcelProperty(value = "当前库存量")
    private Long currentStock;

    /**
     * 最低库存预警值
     */
    @ExcelProperty(value = "最低库存预警值")
    private Long minStockAlert;

    /**
     * 存放位置
     */
    @ExcelProperty(value = "存放位置")
    private String location;

    /**
     * 记录创建时间
     */
    @ExcelProperty(value = "记录创建时间")
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    @ExcelProperty(value = "记录最后更新时间")
    private Date updatedAt;


}
