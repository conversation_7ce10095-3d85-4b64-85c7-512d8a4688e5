package com.mh.lps.domain.bo;

import com.mh.common.mybatis.core.domain.BaseEntity;
import com.mh.lps.domain.LpsMachines;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import com.mh.common.core.validate.AddGroup;
import com.mh.common.core.validate.EditGroup;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 机台基础信息业务对象 lps_machines
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LpsMachines.class, reverseConvertGenerate = false)
public class LpsMachinesBo extends BaseEntity {

    /**
     * 机台ID
     */
    private Long id;

    /**
     * 机台编号，唯一
     */
    private String machineSn;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 所在产线/位置
     */
    @NotBlank(message = "所在产线/位置不能为空", groups = { AddGroup.class, EditGroup.class })
    private String location;

    /**
     * 机台二维码URL
     */
    @NotBlank(message = "机台二维码URL不能为空", groups = { AddGroup.class, EditGroup.class })
    private String qrCodeUrl;

    /**
     * 机台状态：active(正常), in_repair(维修中), out_of_service(停用)
     */
    private String status;

    /**
     * 记录创建时间
     */
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    private Date updatedAt;


}
