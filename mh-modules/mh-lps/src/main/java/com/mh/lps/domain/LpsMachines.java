package com.mh.lps.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.mh.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 机台基础信息对象 lps_machines
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lps_machines")
public class LpsMachines extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 机台ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 机台编号，唯一
     */
    private String machineSn;

    /**
     * 机台名称
     */
    private String machineName;

    /**
     * 所在产线/位置
     */
    private String location;

    /**
     * 机台二维码URL
     */
    private String qrCodeUrl;

    /**
     * 机台状态：active(正常), in_repair(维修中), out_of_service(停用)
     */
    private String status;

    /**
     * 记录创建时间
     */
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    private Date updatedAt;


}
