package com.mh.lps.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.mh.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 零配件基础信息对象 lps_parts
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lps_parts")
public class LpsParts extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 零配件ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 零配件名称
     */
    private String partName;

    /**
     * 零配件型号
     */
    private String partModel;

    /**
     * 零配件编码，唯一
     */
    private String partCode;

    /**
     * 计量单位（如：个、米、套）
     */
    private String unit;

    /**
     * 零配件描述
     */
    private String description;

    /**
     * 当前库存量
     */
    private Long currentStock;

    /**
     * 最低库存预警值
     */
    private Long minStockAlert;

    /**
     * 存放位置
     */
    private String location;

    /**
     * 记录创建时间
     */
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    private Date updatedAt;


}
