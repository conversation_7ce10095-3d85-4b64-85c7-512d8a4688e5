package com.mh.lps.service;

import com.mh.common.mybatis.core.page.TableDataInfo;
import com.mh.common.mybatis.core.page.PageQuery;
import com.mh.lps.domain.bo.LpsRepairRequestsBo;
import com.mh.lps.domain.vo.LpsRepairRequestsVo;

import java.util.Collection;
import java.util.List;

/**
 * 报修请求Service接口
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
public interface ILpsRepairRequestsService {

    /**
     * 查询报修请求
     *
     * @param id 主键
     * @return 报修请求
     */
    LpsRepairRequestsVo queryById(Long id);

    /**
     * 分页查询报修请求列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 报修请求分页列表
     */
    TableDataInfo<LpsRepairRequestsVo> queryPageList(LpsRepairRequestsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的报修请求列表
     *
     * @param bo 查询条件
     * @return 报修请求列表
     */
    List<LpsRepairRequestsVo> queryList(LpsRepairRequestsBo bo);

    /**
     * 新增报修请求
     *
     * @param bo 报修请求
     * @return 是否新增成功
     */
    Boolean insertByBo(LpsRepairRequestsBo bo);

    /**
     * 修改报修请求
     *
     * @param bo 报修请求
     * @return 是否修改成功
     */
    Boolean updateByBo(LpsRepairRequestsBo bo);

    /**
     * 校验并批量删除报修请求信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
