package com.mh.lps.service;

import com.mh.common.mybatis.core.page.TableDataInfo;
import com.mh.common.mybatis.core.page.PageQuery;
import com.mh.lps.domain.bo.LpsPartsBo;
import com.mh.lps.domain.vo.LpsPartsVo;

import java.util.Collection;
import java.util.List;

/**
 * 零配件基础信息Service接口
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
public interface ILpsPartsService {

    /**
     * 查询零配件基础信息
     *
     * @param id 主键
     * @return 零配件基础信息
     */
    LpsPartsVo queryById(Long id);

    /**
     * 分页查询零配件基础信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 零配件基础信息分页列表
     */
    TableDataInfo<LpsPartsVo> queryPageList(LpsPartsBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的零配件基础信息列表
     *
     * @param bo 查询条件
     * @return 零配件基础信息列表
     */
    List<LpsPartsVo> queryList(LpsPartsBo bo);

    /**
     * 新增零配件基础信息
     *
     * @param bo 零配件基础信息
     * @return 是否新增成功
     */
    Boolean insertByBo(LpsPartsBo bo);

    /**
     * 修改零配件基础信息
     *
     * @param bo 零配件基础信息
     * @return 是否修改成功
     */
    Boolean updateByBo(LpsPartsBo bo);

    /**
     * 校验并批量删除零配件基础信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
