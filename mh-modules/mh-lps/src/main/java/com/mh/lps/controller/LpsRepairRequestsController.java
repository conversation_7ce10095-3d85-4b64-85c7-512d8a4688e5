package com.mh.lps.controller;

import java.util.List;

import com.mh.lps.domain.bo.LpsRepairRequestsBo;
import com.mh.lps.domain.vo.LpsRepairRequestsVo;
import com.mh.lps.service.ILpsRepairRequestsService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.mh.common.idempotent.annotation.RepeatSubmit;
import com.mh.common.log.annotation.Log;
import com.mh.common.web.core.BaseController;
import com.mh.common.mybatis.core.page.PageQuery;
import com.mh.common.core.domain.R;
import com.mh.common.core.validate.AddGroup;
import com.mh.common.core.validate.EditGroup;
import com.mh.common.log.enums.BusinessType;
import com.mh.common.excel.utils.ExcelUtil;
import com.mh.common.mybatis.core.page.TableDataInfo;

/**
 * 报修请求
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/lps/repairRequests")
public class LpsRepairRequestsController extends BaseController {

    private final ILpsRepairRequestsService lpsRepairRequestsService;

    /**
     * 查询报修请求列表
     */
    @SaCheckPermission("system:repairRequests:list")
    @GetMapping("/list")
    public TableDataInfo<LpsRepairRequestsVo> list(LpsRepairRequestsBo bo, PageQuery pageQuery) {
        return lpsRepairRequestsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出报修请求列表
     */
    @SaCheckPermission("system:repairRequests:export")
    @Log(title = "报修请求", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LpsRepairRequestsBo bo, HttpServletResponse response) {
        List<LpsRepairRequestsVo> list = lpsRepairRequestsService.queryList(bo);
        ExcelUtil.exportExcel(list, "报修请求", LpsRepairRequestsVo.class, response);
    }

    /**
     * 获取报修请求详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:repairRequests:query")
    @GetMapping("/{id}")
    public R<LpsRepairRequestsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(lpsRepairRequestsService.queryById(id));
    }

    /**
     * 新增报修请求
     */
    @SaCheckPermission("system:repairRequests:add")
    @Log(title = "报修请求", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LpsRepairRequestsBo bo) {
        return toAjax(lpsRepairRequestsService.insertByBo(bo));
    }

    /**
     * 修改报修请求
     */
    @SaCheckPermission("system:repairRequests:edit")
    @Log(title = "报修请求", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LpsRepairRequestsBo bo) {
        return toAjax(lpsRepairRequestsService.updateByBo(bo));
    }

    /**
     * 删除报修请求
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:repairRequests:remove")
    @Log(title = "报修请求", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(lpsRepairRequestsService.deleteWithValidByIds(List.of(ids), true));
    }
}
