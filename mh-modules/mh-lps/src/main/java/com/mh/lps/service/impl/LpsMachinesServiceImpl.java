package com.mh.lps.service.impl;

import com.mh.common.core.utils.MapstructUtils;
import com.mh.common.core.utils.StringUtils;
import com.mh.common.mybatis.core.page.TableDataInfo;
import com.mh.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mh.lps.domain.LpsMachines;
import com.mh.lps.domain.bo.LpsMachinesBo;
import com.mh.lps.domain.vo.LpsMachinesVo;
import com.mh.lps.mapper.LpsMachinesMapper;
import com.mh.lps.service.ILpsMachinesService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 机台基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-03
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LpsMachinesServiceImpl implements ILpsMachinesService {

    private final LpsMachinesMapper baseMapper;

    /**
     * 查询机台基础信息
     *
     * @param id 主键
     * @return 机台基础信息
     */
    @Override
    public LpsMachinesVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询机台基础信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 机台基础信息分页列表
     */
    @Override
    public TableDataInfo<LpsMachinesVo> queryPageList(LpsMachinesBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LpsMachines> lqw = buildQueryWrapper(bo);
        Page<LpsMachinesVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的机台基础信息列表
     *
     * @param bo 查询条件
     * @return 机台基础信息列表
     */
    @Override
    public List<LpsMachinesVo> queryList(LpsMachinesBo bo) {
        LambdaQueryWrapper<LpsMachines> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LpsMachines> buildQueryWrapper(LpsMachinesBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LpsMachines> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LpsMachines::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getMachineSn()), LpsMachines::getMachineSn, bo.getMachineSn());
        lqw.like(StringUtils.isNotBlank(bo.getMachineName()), LpsMachines::getMachineName, bo.getMachineName());
        lqw.eq(StringUtils.isNotBlank(bo.getLocation()), LpsMachines::getLocation, bo.getLocation());
        lqw.eq(StringUtils.isNotBlank(bo.getQrCodeUrl()), LpsMachines::getQrCodeUrl, bo.getQrCodeUrl());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), LpsMachines::getStatus, bo.getStatus());
        lqw.eq(bo.getCreatedAt() != null, LpsMachines::getCreatedAt, bo.getCreatedAt());
        lqw.eq(bo.getUpdatedAt() != null, LpsMachines::getUpdatedAt, bo.getUpdatedAt());
        return lqw;
    }

    /**
     * 新增机台基础信息
     *
     * @param bo 机台基础信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LpsMachinesBo bo) {
        LpsMachines add = MapstructUtils.convert(bo, LpsMachines.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改机台基础信息
     *
     * @param bo 机台基础信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LpsMachinesBo bo) {
        LpsMachines update = MapstructUtils.convert(bo, LpsMachines.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LpsMachines entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除机台基础信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
