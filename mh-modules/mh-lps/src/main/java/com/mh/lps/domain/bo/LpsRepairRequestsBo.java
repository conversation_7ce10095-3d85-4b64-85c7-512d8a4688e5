package com.mh.lps.domain.bo;

import com.mh.common.mybatis.core.domain.BaseEntity;
import com.mh.lps.domain.LpsRepairRequests;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.validation.constraints.*;
import com.mh.common.core.validate.AddGroup;
import com.mh.common.core.validate.EditGroup;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

/**
 * 报修请求业务对象 lps_repair_requests
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = LpsRepairRequests.class, reverseConvertGenerate = false)
public class LpsRepairRequestsBo extends BaseEntity {

    /**
     * 报修请求ID
     */
    private Long id;

    /**
     * 报修单号，唯一
     */
    private String requestSn;

    /**
     * 机台ID，关联lps_machines表
     */
    private Long machineId;

    /**
     * 报修人ID，此处假设用户ID由外部系统提供或在应用层面管理
     */
    private Long requesterId;

    /**
     * 报修时间
     */
    private Date requestTime;

    /**
     * 报修状态：pending(待维修), in_progress(维修中), completed(已完成), cancelled(已取消)
     */
    private String status;

    /**
     * 维修技术人员ID，此处假设用户ID由外部系统提供或在应用层面管理
     */
    @NotNull(message = "维修技术人员ID，此处假设用户ID由外部系统提供或在应用层面管理不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long repairerId;

    /**
     * 技术人员接单时间
     */
    @NotNull(message = "技术人员接单时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date acceptTime;

    /**
     * 维修完成时间
     */
    @NotNull(message = "维修完成时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private Date completeTime;

    /**
     * 故障描述
     */
    @NotBlank(message = "故障描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String description;

    /**
     * 记录创建时间
     */
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    private Date updatedAt;


}
