package com.mh.lps.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.mh.common.mybatis.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serial;

/**
 * 报修请求对象 lps_repair_requests
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lps_repair_requests")
public class LpsRepairRequests extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 报修请求ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 报修单号，唯一
     */
    private String requestSn;

    /**
     * 机台ID，关联lps_machines表
     */
    private Long machineId;

    /**
     * 报修人ID，此处假设用户ID由外部系统提供或在应用层面管理
     */
    private Long requesterId;

    /**
     * 报修时间
     */
    private Date requestTime;

    /**
     * 报修状态：pending(待维修), in_progress(维修中), completed(已完成), cancelled(已取消)
     */
    private String status;

    /**
     * 维修技术人员ID，此处假设用户ID由外部系统提供或在应用层面管理
     */
    private Long repairerId;

    /**
     * 技术人员接单时间
     */
    private Date acceptTime;

    /**
     * 维修完成时间
     */
    private Date completeTime;

    /**
     * 故障描述
     */
    private String description;

    /**
     * 记录创建时间
     */
    private Date createdAt;

    /**
     * 记录最后更新时间
     */
    private Date updatedAt;


}
