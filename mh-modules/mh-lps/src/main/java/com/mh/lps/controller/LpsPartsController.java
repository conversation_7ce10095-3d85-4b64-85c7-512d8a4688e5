package com.mh.lps.controller;

import java.util.List;

import com.mh.lps.domain.bo.LpsPartsBo;
import com.mh.lps.domain.vo.LpsPartsVo;
import com.mh.lps.service.ILpsPartsService;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.mh.common.idempotent.annotation.RepeatSubmit;
import com.mh.common.log.annotation.Log;
import com.mh.common.web.core.BaseController;
import com.mh.common.mybatis.core.page.PageQuery;
import com.mh.common.core.domain.R;
import com.mh.common.core.validate.AddGroup;
import com.mh.common.core.validate.EditGroup;
import com.mh.common.log.enums.BusinessType;
import com.mh.common.excel.utils.ExcelUtil;
import com.mh.common.mybatis.core.page.TableDataInfo;

/**
 * 零配件基础信息
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/lps/parts")
public class LpsPartsController extends BaseController {

    private final ILpsPartsService lpsPartsService;

    /**
     * 查询零配件基础信息列表
     */
    @SaCheckPermission("system:parts:list")
    @GetMapping("/list")
    public TableDataInfo<LpsPartsVo> list(LpsPartsBo bo, PageQuery pageQuery) {
        return lpsPartsService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出零配件基础信息列表
     */
    @SaCheckPermission("system:parts:export")
    @Log(title = "零配件基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(LpsPartsBo bo, HttpServletResponse response) {
        List<LpsPartsVo> list = lpsPartsService.queryList(bo);
        ExcelUtil.exportExcel(list, "零配件基础信息", LpsPartsVo.class, response);
    }

    /**
     * 获取零配件基础信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("system:parts:query")
    @GetMapping("/{id}")
    public R<LpsPartsVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(lpsPartsService.queryById(id));
    }

    /**
     * 新增零配件基础信息
     */
    @SaCheckPermission("system:parts:add")
    @Log(title = "零配件基础信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody LpsPartsBo bo) {
        return toAjax(lpsPartsService.insertByBo(bo));
    }

    /**
     * 修改零配件基础信息
     */
    @SaCheckPermission("system:parts:edit")
    @Log(title = "零配件基础信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody LpsPartsBo bo) {
        return toAjax(lpsPartsService.updateByBo(bo));
    }

    /**
     * 删除零配件基础信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("system:parts:remove")
    @Log(title = "零配件基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(lpsPartsService.deleteWithValidByIds(List.of(ids), true));
    }
}
