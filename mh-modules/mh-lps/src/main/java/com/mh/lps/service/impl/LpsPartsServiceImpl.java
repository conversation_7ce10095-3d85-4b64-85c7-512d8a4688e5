package com.mh.lps.service.impl;

import com.mh.common.core.utils.MapstructUtils;
import com.mh.common.core.utils.StringUtils;
import com.mh.common.mybatis.core.page.TableDataInfo;
import com.mh.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mh.lps.domain.LpsParts;
import com.mh.lps.domain.bo.LpsPartsBo;
import com.mh.lps.domain.vo.LpsPartsVo;
import com.mh.lps.mapper.LpsPartsMapper;
import com.mh.lps.service.ILpsPartsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 零配件基础信息Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LpsPartsServiceImpl implements ILpsPartsService {

    private final LpsPartsMapper baseMapper;

    /**
     * 查询零配件基础信息
     *
     * @param id 主键
     * @return 零配件基础信息
     */
    @Override
    public LpsPartsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询零配件基础信息列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 零配件基础信息分页列表
     */
    @Override
    public TableDataInfo<LpsPartsVo> queryPageList(LpsPartsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LpsParts> lqw = buildQueryWrapper(bo);
        Page<LpsPartsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的零配件基础信息列表
     *
     * @param bo 查询条件
     * @return 零配件基础信息列表
     */
    @Override
    public List<LpsPartsVo> queryList(LpsPartsBo bo) {
        LambdaQueryWrapper<LpsParts> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LpsParts> buildQueryWrapper(LpsPartsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LpsParts> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LpsParts::getId);
        lqw.like(StringUtils.isNotBlank(bo.getPartName()), LpsParts::getPartName, bo.getPartName());
        lqw.eq(StringUtils.isNotBlank(bo.getPartModel()), LpsParts::getPartModel, bo.getPartModel());
        lqw.eq(StringUtils.isNotBlank(bo.getPartCode()), LpsParts::getPartCode, bo.getPartCode());
        lqw.eq(StringUtils.isNotBlank(bo.getUnit()), LpsParts::getUnit, bo.getUnit());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), LpsParts::getDescription, bo.getDescription());
        lqw.eq(bo.getCurrentStock() != null, LpsParts::getCurrentStock, bo.getCurrentStock());
        lqw.eq(bo.getMinStockAlert() != null, LpsParts::getMinStockAlert, bo.getMinStockAlert());
        lqw.eq(StringUtils.isNotBlank(bo.getLocation()), LpsParts::getLocation, bo.getLocation());
        lqw.eq(bo.getCreatedAt() != null, LpsParts::getCreatedAt, bo.getCreatedAt());
        lqw.eq(bo.getUpdatedAt() != null, LpsParts::getUpdatedAt, bo.getUpdatedAt());
        return lqw;
    }

    /**
     * 新增零配件基础信息
     *
     * @param bo 零配件基础信息
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LpsPartsBo bo) {
        LpsParts add = MapstructUtils.convert(bo, LpsParts.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改零配件基础信息
     *
     * @param bo 零配件基础信息
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LpsPartsBo bo) {
        LpsParts update = MapstructUtils.convert(bo, LpsParts.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LpsParts entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除零配件基础信息信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
