package com.mh.lps.service.impl;

import com.mh.common.core.utils.MapstructUtils;
import com.mh.common.core.utils.StringUtils;
import com.mh.common.mybatis.core.page.TableDataInfo;
import com.mh.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mh.lps.domain.LpsRepairRequests;
import com.mh.lps.domain.bo.LpsRepairRequestsBo;
import com.mh.lps.domain.vo.LpsRepairRequestsVo;
import com.mh.lps.mapper.LpsRepairRequestsMapper;
import com.mh.lps.service.ILpsRepairRequestsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 报修请求Service业务层处理
 *
 * <AUTHOR> Li
 * @date 2025-08-03
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LpsRepairRequestsServiceImpl implements ILpsRepairRequestsService {

    private final LpsRepairRequestsMapper baseMapper;

    /**
     * 查询报修请求
     *
     * @param id 主键
     * @return 报修请求
     */
    @Override
    public LpsRepairRequestsVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询报修请求列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 报修请求分页列表
     */
    @Override
    public TableDataInfo<LpsRepairRequestsVo> queryPageList(LpsRepairRequestsBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LpsRepairRequests> lqw = buildQueryWrapper(bo);
        Page<LpsRepairRequestsVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的报修请求列表
     *
     * @param bo 查询条件
     * @return 报修请求列表
     */
    @Override
    public List<LpsRepairRequestsVo> queryList(LpsRepairRequestsBo bo) {
        LambdaQueryWrapper<LpsRepairRequests> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<LpsRepairRequests> buildQueryWrapper(LpsRepairRequestsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LpsRepairRequests> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LpsRepairRequests::getId);
        lqw.eq(StringUtils.isNotBlank(bo.getRequestSn()), LpsRepairRequests::getRequestSn, bo.getRequestSn());
        lqw.eq(bo.getMachineId() != null, LpsRepairRequests::getMachineId, bo.getMachineId());
        lqw.eq(bo.getRequesterId() != null, LpsRepairRequests::getRequesterId, bo.getRequesterId());
        lqw.eq(bo.getRequestTime() != null, LpsRepairRequests::getRequestTime, bo.getRequestTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), LpsRepairRequests::getStatus, bo.getStatus());
        lqw.eq(bo.getRepairerId() != null, LpsRepairRequests::getRepairerId, bo.getRepairerId());
        lqw.eq(bo.getAcceptTime() != null, LpsRepairRequests::getAcceptTime, bo.getAcceptTime());
        lqw.eq(bo.getCompleteTime() != null, LpsRepairRequests::getCompleteTime, bo.getCompleteTime());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), LpsRepairRequests::getDescription, bo.getDescription());
        lqw.eq(bo.getCreatedAt() != null, LpsRepairRequests::getCreatedAt, bo.getCreatedAt());
        lqw.eq(bo.getUpdatedAt() != null, LpsRepairRequests::getUpdatedAt, bo.getUpdatedAt());
        return lqw;
    }

    /**
     * 新增报修请求
     *
     * @param bo 报修请求
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LpsRepairRequestsBo bo) {
        LpsRepairRequests add = MapstructUtils.convert(bo, LpsRepairRequests.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改报修请求
     *
     * @param bo 报修请求
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LpsRepairRequestsBo bo) {
        LpsRepairRequests update = MapstructUtils.convert(bo, LpsRepairRequests.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LpsRepairRequests entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除报修请求信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }
}
