<template>
  <view class="repair-container">
    <view class="header">
      <text class="title">设备报修</text>
    </view>
    
    <view class="form-container">
      <view class="form-item">
        <text class="label">设备名称</text>
        <input class="input" v-model="repairForm.deviceName" placeholder="请输入设备名称" />
      </view>
      
      <view class="form-item">
        <text class="label">设备编号</text>
        <input class="input" v-model="repairForm.deviceCode" placeholder="请输入设备编号" />
      </view>
      
      <view class="form-item">
        <text class="label">故障描述</text>
        <textarea class="textarea" v-model="repairForm.faultDesc" placeholder="请详细描述设备故障情况" />
      </view>
      
      <view class="form-item">
        <text class="label">紧急程度</text>
        <picker @change="onUrgencyChange" :value="urgencyIndex" :range="urgencyList">
          <view class="picker">
            {{urgencyList[urgencyIndex]}}
          </view>
        </picker>
      </view>
      
      <view class="form-item">
        <text class="label">故障图片</text>
        <view class="image-upload">
          <view class="upload-btn" @click="chooseImage">
            <text>+</text>
            <text>上传图片</text>
          </view>
          <view class="image-list">
            <view class="image-item" v-for="(item, index) in imageList" :key="index">
              <image :src="item" mode="aspectFill"></image>
              <view class="delete-btn" @click="deleteImage(index)">×</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <view class="btn-container">
      <button class="submit-btn" @click="submitRepair">提交报修</button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      repairForm: {
        deviceName: '',
        deviceCode: '',
        faultDesc: '',
        urgency: '一般'
      },
      urgencyList: ['一般', '紧急', '非常紧急'],
      urgencyIndex: 0,
      imageList: []
    }
  },
  methods: {
    onUrgencyChange(e) {
      this.urgencyIndex = e.detail.value
      this.repairForm.urgency = this.urgencyList[e.detail.value]
    },
    
    chooseImage() {
      uni.chooseImage({
        count: 3,
        success: (res) => {
          this.imageList = this.imageList.concat(res.tempFilePaths)
        }
      })
    },
    
    deleteImage(index) {
      this.imageList.splice(index, 1)
    },
    
    submitRepair() {
      if (!this.repairForm.deviceName) {
        uni.showToast({
          title: '请输入设备名称',
          icon: 'none'
        })
        return
      }
      
      if (!this.repairForm.faultDesc) {
        uni.showToast({
          title: '请输入故障描述',
          icon: 'none'
        })
        return
      }
      
      // 这里可以调用API提交报修信息
      uni.showToast({
        title: '报修提交成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  }
}
</script>

<style scoped>
.repair-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.form-container {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.form-item {
  margin-bottom: 30rpx;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.input {
  width: 100%;
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.textarea {
  width: 100%;
  height: 150rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.picker {
  height: 80rpx;
  line-height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  background-color: #fff;
}

.image-upload {
  display: flex;
  flex-wrap: wrap;
}

.upload-btn {
  width: 150rpx;
  height: 150rpx;
  border: 2rpx dashed #ccc;
  border-radius: 10rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
  font-size: 24rpx;
  color: #999;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 150rpx;
  height: 150rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.delete-btn {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
}

.btn-container {
  padding: 0 30rpx;
}

.submit-btn {
  width: 100%;
  height: 80rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
}
</style>