<template>
  <view class="device-container">
    <view class="header">
      <text class="title">设备管理</text>
    </view>
    
    <view class="menu-grid">
      <view class="menu-item" @click="navigateTo('/pages/device/repair/index')">
        <view class="menu-icon">
          <image src="/static/images/device/repair.png" mode="aspectFit"></image>
        </view>
        <text class="menu-text">设备报修</text>
      </view>
      
      <view class="menu-item" @click="navigateTo('/pages/device/maintenance/index')">
        <view class="menu-icon">
          <image src="/static/images/device/maintenance.png" mode="aspectFit"></image>
        </view>
        <text class="menu-text">维修接单</text>
      </view>
      
      <view class="menu-item" @click="navigateTo('/pages/device/material/index')">
        <view class="menu-icon">
          <image src="/static/images/device/material.png" mode="aspectFit"></image>
        </view>
        <text class="menu-text">设备领料</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      
    }
  },
  methods: {
    navigateTo(url) {
      uni.navigateTo({
        url: url
      })
    }
  }
}
</script>

<style scoped>
.device-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.menu-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-around;
  padding: 20rpx;
}

.menu-item {
  width: 200rpx;
  height: 200rpx;
  background-color: #fff;
  border-radius: 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.menu-icon image {
  width: 100%;
  height: 100%;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
}
</style>