<template>
  <view class="maintenance-container">
    <view class="header">
      <text class="title">维修接单</text>
    </view>
    
    <view class="filter-bar">
      <picker @change="onStatusChange" :value="statusIndex" :range="statusList">
        <view class="filter-item">
          {{statusList[statusIndex]}}
        </view>
      </picker>
    </view>
    
    <view class="order-list">
      <view class="order-item" v-for="(item, index) in orderList" :key="index" @click="viewDetail(item)">
        <view class="order-header">
          <text class="order-no">工单号：{{item.orderNo}}</text>
          <view class="status-tag" :class="getStatusClass(item.status)">
            <text>{{item.status}}</text>
          </view>
        </view>
        
        <view class="order-content">
          <view class="info-row">
            <text class="label">设备名称：</text>
            <text class="value">{{item.deviceName}}</text>
          </view>
          <view class="info-row">
            <text class="label">设备编号：</text>
            <text class="value">{{item.deviceCode}}</text>
          </view>
          <view class="info-row">
            <text class="label">故障描述：</text>
            <text class="value">{{item.faultDesc}}</text>
          </view>
          <view class="info-row">
            <text class="label">紧急程度：</text>
            <text class="value urgency" :class="getUrgencyClass(item.urgency)">{{item.urgency}}</text>
          </view>
          <view class="info-row">
            <text class="label">报修时间：</text>
            <text class="value">{{item.createTime}}</text>
          </view>
        </view>
        
        <view class="order-actions" v-if="item.status === '待接单'">
          <button class="action-btn accept-btn" @click.stop="acceptOrder(item)">接单</button>
          <button class="action-btn reject-btn" @click.stop="rejectOrder(item)">拒绝</button>
        </view>
        
        <view class="order-actions" v-if="item.status === '维修中'">
          <button class="action-btn complete-btn" @click.stop="completeOrder(item)">完成维修</button>
        </view>
      </view>
    </view>
    
    <view class="empty-state" v-if="orderList.length === 0">
      <text>暂无维修工单</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      statusList: ['全部', '待接单', '维修中', '已完成'],
      statusIndex: 0,
      orderList: [
        {
          orderNo: 'WX202312010001',
          deviceName: '印刷机A1',
          deviceCode: 'YSJ001',
          faultDesc: '设备异常响声，疑似轴承问题',
          urgency: '紧急',
          status: '待接单',
          createTime: '2023-12-01 09:30:00'
        },
        {
          orderNo: 'WX202312010002',
          deviceName: '切纸机B2',
          deviceCode: 'QZJ002',
          faultDesc: '切纸精度不准确',
          urgency: '一般',
          status: '维修中',
          createTime: '2023-12-01 10:15:00'
        },
        {
          orderNo: 'WX202312010003',
          deviceName: '包装机C3',
          deviceCode: 'BZJ003',
          faultDesc: '包装袋封口不严',
          urgency: '非常紧急',
          status: '待接单',
          createTime: '2023-12-01 11:00:00'
        }
      ]
    }
  },
  computed: {
    filteredOrderList() {
      if (this.statusIndex === 0) {
        return this.orderList
      }
      const status = this.statusList[this.statusIndex]
      return this.orderList.filter(item => item.status === status)
    }
  },
  methods: {
    onStatusChange(e) {
      this.statusIndex = e.detail.value
    },
    
    getStatusClass(status) {
      switch (status) {
        case '待接单': return 'status-pending'
        case '维修中': return 'status-processing'
        case '已完成': return 'status-completed'
        default: return ''
      }
    },
    
    getUrgencyClass(urgency) {
      switch (urgency) {
        case '一般': return 'urgency-normal'
        case '紧急': return 'urgency-urgent'
        case '非常紧急': return 'urgency-critical'
        default: return ''
      }
    },
    
    viewDetail(item) {
      // 查看工单详情
      uni.showModal({
        title: '工单详情',
        content: `设备：${item.deviceName}\n故障：${item.faultDesc}`,
        showCancel: false
      })
    },
    
    acceptOrder(item) {
      uni.showModal({
        title: '确认接单',
        content: `确定要接受工单 ${item.orderNo} 吗？`,
        success: (res) => {
          if (res.confirm) {
            item.status = '维修中'
            uni.showToast({
              title: '接单成功',
              icon: 'success'
            })
          }
        }
      })
    },
    
    rejectOrder(item) {
      uni.showModal({
        title: '拒绝接单',
        content: `确定要拒绝工单 ${item.orderNo} 吗？`,
        success: (res) => {
          if (res.confirm) {
            uni.showToast({
              title: '已拒绝工单',
              icon: 'none'
            })
          }
        }
      })
    },
    
    completeOrder(item) {
      uni.showModal({
        title: '完成维修',
        content: `确定已完成工单 ${item.orderNo} 的维修吗？`,
        success: (res) => {
          if (res.confirm) {
            item.status = '已完成'
            uni.showToast({
              title: '维修完成',
              icon: 'success'
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.maintenance-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.filter-bar {
  margin-bottom: 30rpx;
}

.filter-item {
  height: 60rpx;
  line-height: 60rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  text-align: center;
}

.order-list {
  margin-bottom: 40rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-no {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-processing {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.order-content {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 26rpx;
}

.label {
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  color: #333;
  flex: 1;
}

.urgency.urgency-normal {
  color: #28a745;
}

.urgency.urgency-urgent {
  color: #ffc107;
}

.urgency.urgency-critical {
  color: #dc3545;
}

.order-actions {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  border: none;
}

.accept-btn {
  background-color: #28a745;
  color: #fff;
}

.reject-btn {
  background-color: #dc3545;
  color: #fff;
}

.complete-btn {
  background-color: #007bff;
  color: #fff;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}
</style>