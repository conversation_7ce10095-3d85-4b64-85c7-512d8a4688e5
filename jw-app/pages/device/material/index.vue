<template>
  <view class="material-container">
    <view class="header">
      <text class="title">设备领料</text>
    </view>
    
    <view class="search-bar">
      <input class="search-input" v-model="searchKeyword" placeholder="搜索物料名称或编号" @input="onSearch" />
    </view>
    
    <view class="category-tabs">
      <view class="tab-item" 
            v-for="(item, index) in categoryList" 
            :key="index"
            :class="{active: currentCategory === index}"
            @click="switchCategory(index)">
        <text>{{item}}</text>
      </view>
    </view>
    
    <view class="material-list">
      <view class="material-item" v-for="(item, index) in filteredMaterialList" :key="index">
        <view class="material-info">
          <view class="material-header">
            <text class="material-name">{{item.name}}</text>
            <text class="material-code">{{item.code}}</text>
          </view>
          <view class="material-details">
            <view class="detail-row">
              <text class="label">规格型号：</text>
              <text class="value">{{item.specification}}</text>
            </view>
            <view class="detail-row">
              <text class="label">库存数量：</text>
              <text class="value stock" :class="getStockClass(item.stock)">{{item.stock}} {{item.unit}}</text>
            </view>
            <view class="detail-row">
              <text class="label">存放位置：</text>
              <text class="value">{{item.location}}</text>
            </view>
          </view>
        </view>
        
        <view class="material-actions">
          <view class="quantity-control">
            <button class="quantity-btn" @click="decreaseQuantity(index)">-</button>
            <input class="quantity-input" v-model.number="item.requestQuantity" type="number" />
            <button class="quantity-btn" @click="increaseQuantity(index)">+</button>
          </view>
          <button class="add-btn" @click="addToCart(item)" :disabled="!item.requestQuantity || item.requestQuantity <= 0">
            加入申请
          </button>
        </view>
      </view>
    </view>
    
    <view class="empty-state" v-if="filteredMaterialList.length === 0">
      <text>暂无物料信息</text>
    </view>
    
    <!-- 购物车浮动按钮 -->
    <view class="cart-float" @click="showCart" v-if="cartList.length > 0">
      <view class="cart-badge">{{cartList.length}}</view>
      <text>申请清单</text>
    </view>
    
    <!-- 申请清单弹窗 -->
    <view class="cart-modal" v-if="showCartModal" @click="hideCart">
      <view class="cart-content" @click.stop>
        <view class="cart-header">
          <text class="cart-title">申请清单</text>
          <text class="close-btn" @click="hideCart">×</text>
        </view>
        
        <view class="cart-list">
          <view class="cart-item" v-for="(item, index) in cartList" :key="index">
            <view class="cart-item-info">
              <text class="cart-item-name">{{item.name}}</text>
              <text class="cart-item-spec">{{item.specification}}</text>
            </view>
            <view class="cart-item-quantity">
              <text>{{item.requestQuantity}} {{item.unit}}</text>
              <text class="remove-btn" @click="removeFromCart(index)">删除</text>
            </view>
          </view>
        </view>
        
        <view class="cart-actions">
          <button class="submit-cart-btn" @click="submitApplication">提交申请</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      searchKeyword: '',
      currentCategory: 0,
      categoryList: ['全部', '机械配件', '电气元件', '润滑油料', '工具耗材'],
      showCartModal: false,
      cartList: [],
      materialList: [
        {
          id: 1,
          name: '轴承',
          code: 'ZC001',
          specification: '6205-2RS',
          stock: 50,
          unit: '个',
          location: 'A区-01-03',
          category: '机械配件',
          requestQuantity: 0
        },
        {
          id: 2,
          name: '电机',
          code: 'DJ001',
          specification: '1.5KW 380V',
          stock: 8,
          unit: '台',
          location: 'B区-02-01',
          category: '电气元件',
          requestQuantity: 0
        },
        {
          id: 3,
          name: '液压油',
          code: 'YY001',
          specification: '46#抗磨液压油',
          stock: 120,
          unit: 'L',
          location: 'C区-03-05',
          category: '润滑油料',
          requestQuantity: 0
        },
        {
          id: 4,
          name: '密封圈',
          code: 'MF001',
          specification: 'O型圈 φ50×3',
          stock: 200,
          unit: '个',
          location: 'A区-01-08',
          category: '机械配件',
          requestQuantity: 0
        },
        {
          id: 5,
          name: '扳手',
          code: 'BS001',
          specification: '开口扳手 17mm',
          stock: 5,
          unit: '把',
          location: 'D区-04-02',
          category: '工具耗材',
          requestQuantity: 0
        }
      ]
    }
  },
  computed: {
    filteredMaterialList() {
      let list = this.materialList
      
      // 按分类筛选
      if (this.currentCategory > 0) {
        const category = this.categoryList[this.currentCategory]
        list = list.filter(item => item.category === category)
      }
      
      // 按关键词搜索
      if (this.searchKeyword) {
        list = list.filter(item => 
          item.name.includes(this.searchKeyword) || 
          item.code.includes(this.searchKeyword)
        )
      }
      
      return list
    }
  },
  methods: {
    onSearch() {
      // 搜索逻辑已在computed中处理
    },
    
    switchCategory(index) {
      this.currentCategory = index
    },
    
    getStockClass(stock) {
      if (stock <= 10) return 'stock-low'
      if (stock <= 50) return 'stock-medium'
      return 'stock-high'
    },
    
    decreaseQuantity(index) {
      const item = this.filteredMaterialList[index]
      if (item.requestQuantity > 0) {
        item.requestQuantity--
      }
    },
    
    increaseQuantity(index) {
      const item = this.filteredMaterialList[index]
      if (item.requestQuantity < item.stock) {
        item.requestQuantity++
      } else {
        uni.showToast({
          title: '申请数量不能超过库存',
          icon: 'none'
        })
      }
    },
    
    addToCart(item) {
      if (!item.requestQuantity || item.requestQuantity <= 0) {
        uni.showToast({
          title: '请输入申请数量',
          icon: 'none'
        })
        return
      }
      
      // 检查是否已在申请清单中
      const existIndex = this.cartList.findIndex(cartItem => cartItem.id === item.id)
      if (existIndex >= 0) {
        this.cartList[existIndex].requestQuantity += item.requestQuantity
      } else {
        this.cartList.push({
          ...item,
          requestQuantity: item.requestQuantity
        })
      }
      
      item.requestQuantity = 0
      
      uni.showToast({
        title: '已加入申请清单',
        icon: 'success'
      })
    },
    
    showCart() {
      this.showCartModal = true
    },
    
    hideCart() {
      this.showCartModal = false
    },
    
    removeFromCart(index) {
      this.cartList.splice(index, 1)
    },
    
    submitApplication() {
      if (this.cartList.length === 0) {
        uni.showToast({
          title: '申请清单为空',
          icon: 'none'
        })
        return
      }
      
      uni.showModal({
        title: '确认提交',
        content: `确定要提交 ${this.cartList.length} 项物料申请吗？`,
        success: (res) => {
          if (res.confirm) {
            // 这里可以调用API提交申请
            uni.showToast({
              title: '申请提交成功',
              icon: 'success'
            })
            
            this.cartList = []
            this.showCartModal = false
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.material-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.search-bar {
  margin-bottom: 30rpx;
}

.search-input {
  width: 100%;
  height: 70rpx;
  background-color: #fff;
  border-radius: 35rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}

.category-tabs {
  display: flex;
  margin-bottom: 30rpx;
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  height: 70rpx;
  line-height: 70rpx;
  text-align: center;
  font-size: 26rpx;
  color: #666;
}

.tab-item.active {
  background-color: #007aff;
  color: #fff;
}

.material-list {
  margin-bottom: 40rpx;
}

.material-item {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.material-info {
  margin-bottom: 20rpx;
}

.material-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.material-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.material-code {
  font-size: 24rpx;
  color: #999;
}

.material-details {
  
}

.detail-row {
  display: flex;
  margin-bottom: 8rpx;
  font-size: 26rpx;
}

.label {
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  color: #333;
  flex: 1;
}

.stock.stock-low {
  color: #dc3545;
}

.stock.stock-medium {
  color: #ffc107;
}

.stock.stock-high {
  color: #28a745;
}

.material-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #f0f0f0;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-input {
  width: 100rpx;
  height: 60rpx;
  text-align: center;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  margin: 0 10rpx;
  font-size: 26rpx;
}

.add-btn {
  padding: 15rpx 30rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.add-btn:disabled {
  background-color: #ccc;
}

.empty-state {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

.cart-float {
  position: fixed;
  bottom: 40rpx;
  right: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background-color: #007aff;
  border-radius: 60rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.3);
}

.cart-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: #ff4757;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
}

.cart-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 1000;
}

.cart-content {
  width: 100%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.cart-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.close-btn {
  font-size: 40rpx;
  color: #999;
}

.cart-list {
  max-height: 400rpx;
  overflow-y: auto;
  margin-bottom: 30rpx;
}

.cart-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}

.cart-item-info {
  flex: 1;
}

.cart-item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 5rpx;
}

.cart-item-spec {
  font-size: 24rpx;
  color: #999;
}

.cart-item-quantity {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.remove-btn {
  color: #dc3545;
  font-size: 24rpx;
}

.cart-actions {
  
}

.submit-cart-btn {
  width: 100%;
  height: 80rpx;
  background-color: #007aff;
  color: #fff;
  border: none;
  border-radius: 10rpx;
  font-size: 32rpx;
}
</style>