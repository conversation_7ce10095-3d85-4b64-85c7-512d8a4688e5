## 1.2.3（2024-04-02）
- 修复 修复在微信小程序下inactiveColor失效bug
## 1.2.2（2024-03-28）
- 修复 在vue2下:style动态绑定导致编译失败的bug
## 1.2.1（2024-03-20）
- 新增 inActiveColor属性，可供配置未激活时的颜色
## 1.2.0（2021-11-19）
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-segmented-control](https://uniapp.dcloud.io/component/uniui/uni-segmented-control)
## 1.1.0（2021-07-30）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.0.5（2021-05-12）
- 新增 项目示例地址
## 1.0.4（2021-02-05）
- 调整为uni_modules目录规范
