# APP样式修复说明

## 问题描述
JW-app在H5 web环境下样式正常，但打包成APK后登录页面和首页的样式丢失，特别是背景样式丢失。

## 问题原因分析
1. **CSS3特性兼容性问题**：`backdrop-filter`属性在APP环境中支持有限
2. **渐变背景兼容性**：CSS渐变在某些APP环境下可能不生效
3. **样式优先级问题**：APP环境下可能需要更高的样式优先级

## 解决方案

### 1. 特定页面背景兼容
- 移除全局页面背景设置，避免影响所有页面
- 只对登录页面和首页的容器添加背景样式
- 确保工作台和我的页面保持原有样式

### 2. backdrop-filter替代方案
- H5环境：保持使用`backdrop-filter: blur()`
- APP环境：使用纯色背景和阴影效果替代

### 3. 创建专用兼容文件
- 新建`app-compatibility.scss`文件
- 使用`!important`确保样式优先级
- 移除全局背景设置，只针对特定容器

### 4. 添加工具类
- `.gradient-bg`：跨浏览器渐变背景
- `.force-bg`：强制背景显示

## 修改的文件

### 1. 页面文件
- `pages/login.vue`：修复登录页面背景和backdrop-filter
- `pages/index.vue`：修复首页背景和backdrop-filter

### 2. 样式文件
- `App.vue`：移除全局page背景设置
- `static/scss/app-compatibility.scss`：新建APP兼容样式文件（移除全局背景，只针对特定容器）
- `static/scss/index.scss`：引入兼容样式文件

### 3. 配置文件
- `manifest.json`：保持transformPx为false

### 4. 保持不变的文件
- `pages/work/index.vue`：工作台页面保持原有背景样式
- `pages/mine/index.vue`：我的页面保持原有背景样式

## 技术要点

### 条件编译使用
```scss
/* H5环境 */
/* #ifdef H5 */
backdrop-filter: blur(20px);
/* #endif */

/* APP环境 */
/* #ifndef H5 */
background: rgba(255, 255, 255, 0.98);
/* #endif */
```

### 渐变背景兼容写法
```scss
.gradient-bg {
  background: #667eea;
  background: -webkit-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background: -moz-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background: -o-linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
```

## 测试建议
1. 重新打包APP并测试背景显示
2. 检查登录页面和首页的视觉效果
3. 确认所有卡片组件的背景正常
4. 验证装饰元素的显示效果

## 注意事项
- 使用了`!important`确保样式优先级，请谨慎修改
- 条件编译确保H5和APP环境的兼容性
- 如需添加新页面，请参考现有页面的背景设置方案