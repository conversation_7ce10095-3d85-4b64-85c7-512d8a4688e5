######################################################################
# Build Tools

.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

target/
!.mvn/wrapper/maven-wrapper.jar

######################################################################
# IDE

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr

### JRebel ###
rebel.xml

### NetBeans ###
nbproject/private/
build/*
nbbuild/
nbdist/
.nb-gradle/

######################################################################
# Others
*.log
*.xml.versionsBackup
*.swp

!*/build/*.java
!*/build/*.html
!*/build/*.xml

.flattened-pom.xml


######################################################################
#前端
.DS_Store
.history
node_modules/
dist/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
**/*.log

tests/**/coverage/
tests/e2e/reports
selenium-debug.log

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.local

package-lock.json
yarn.lock
pnpm-lock.yaml

# 编译生成的文件
auto-imports.d.ts
components.d.ts
