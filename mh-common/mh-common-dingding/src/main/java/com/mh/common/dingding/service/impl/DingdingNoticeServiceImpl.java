package com.mh.common.dingding.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkrobot_1_0.Client;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOHeaders;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTORequest;
import com.aliyun.dingtalkrobot_1_0.models.BatchSendOTOResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.mh.common.dingding.config.DingDingProperties;
import com.mh.common.dingding.service.DingdingNoticeService;
import com.mh.common.core.utils.StringUtils;
import com.mh.common.redis.utils.RedisUtils;
import com.taobao.api.FileItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * impl丁鼎通知服务
 *
 * <AUTHOR>
 * @date 2021/12/24
 */
@Service
@Slf4j
public class DingdingNoticeServiceImpl implements DingdingNoticeService {

    private final DingDingProperties dingDingProperties;

    private String baseUrl = "https://oapi.dingtalk.com/topapi";

    public DingdingNoticeServiceImpl(DingDingProperties dingDingProperties) {
        this.dingDingProperties = dingDingProperties;
    }



    @Override
    public String getDingToken() throws Exception {
        String token = RedisUtils.getCacheObject("dingding:token");
        if(StringUtils.isNotEmpty(token)){
            return token;
        }else {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/gettoken");
            OapiGettokenRequest req = new OapiGettokenRequest();
            req.setAppkey(dingDingProperties.getAppKey());
            req.setAppsecret(dingDingProperties.getAppSecret());
            req.setHttpMethod("GET");
            OapiGettokenResponse rsp = client.execute(req);
            String accessToken = JSONObject.parseObject(rsp.getBody()).getString("access_token");
            RedisUtils.setCacheObject("dingding:token", accessToken, java.time.Duration.ofSeconds(3600));
            return accessToken;
        }
    }

    /**
     * 得到丁用户id
     *
     * @return {@link String}
     */
    @Override
    public String getDingUserID(String mobile) {
        String userId = "";
        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/getbymobile");
            OapiV2UserGetbymobileRequest req = new OapiV2UserGetbymobileRequest();
            req.setMobile(mobile);
            OapiV2UserGetbymobileResponse rsp = client.execute(req, getDingToken());
            userId = JSONObject.parseObject(rsp.getBody()).getJSONObject("result").getString("userid");
        } catch (Exception e) {
            log.error("调用钉钉接口报错!{}", e.getMessage());
        }
        return userId;
    }

    //获取部门ID列表
    @Override
    public List<Long> getDeptIdList(Long deptId){

        try {
            DingTalkClient client = new DefaultDingTalkClient(baseUrl+"/v2/department/listsubid");
            OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
            req.setDeptId(deptId);
            OapiV2DepartmentListsubResponse rsp = client.execute(req, getDingToken());
            JSONArray array = JSONObject.parseObject(rsp.getBody()).getJSONObject("result").getJSONArray("dept_id_list");
            System.out.println(rsp.getBody());
            List<Long> deptIdList = array.toJavaList(Long.class);
            return deptIdList;
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    //获取部门列表
    @Override
    public JSONArray getDeptList(Long deptId){

        try {
            DingTalkClient client = new DefaultDingTalkClient(baseUrl+"/v2/department/listsub");
            OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
            req.setDeptId(deptId);
            OapiV2DepartmentListsubResponse rsp = client.execute(req, getDingToken());
            JSONArray array = JSONObject.parseObject(rsp.getBody()).getJSONArray("result");
            System.out.println(rsp.getBody());
            return array;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    //获取部门用户详情
    @Override
    public JSONArray getUserList(Long deptId){
        try {
            DingTalkClient client = new DefaultDingTalkClient(baseUrl+"/v2/user/list");
            OapiV2UserListRequest req = new OapiV2UserListRequest();
            req.setDeptId(deptId);
            req.setCursor(0L);
            req.setSize(100L);
            OapiV2UserListResponse rsp = client.execute(req, getDingToken());
            JSONArray array = JSONObject.parseObject(rsp.getBody()).getJSONObject("result").getJSONArray("list");
            System.out.println(rsp.getBody());
            return array;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 发出叮的工作信息--文本消息
     *  @param text    文本
     * @param userIds 用户id
     * @return
     */
    @Override
    public boolean sendDingWorkMessage(String text, List<String> userIds) throws Exception {
        log.info("发送钉钉工作通知:{}",text);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
        req.setAgentId(Long.valueOf(dingDingProperties.getAgentId()));
        List<String> strings = userIds.stream().map(this::getDingUserID).collect(Collectors.toList());
        req.setUseridList(String.join(",", strings));
        OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        obj1.setMsgtype("text");
        OapiMessageCorpconversationAsyncsendV2Request.Text obj2 = new OapiMessageCorpconversationAsyncsendV2Request.Text();
        obj2.setContent(text);
        obj1.setText(obj2);
        req.setMsg(obj1);
        OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, getDingToken());
        JSONObject jsonObject = JSONObject.parseObject(rsp.getBody());
        String errmsg = jsonObject.getString("errmsg");
        if(StringUtils.equals(errmsg,"ok")){
            log.info("钉钉消息发送成功!发送内容为:{}-接收人为{}",text,JSONObject.toJSONString(userIds));
            return true;
        }
        return false;
    }

    /**
     * 发出钉钉工作消息-卡片消息action_card，带链接的
     * @param title
     * @param text
     * @param url
     * @param userIds
     * @return
     * @throws Exception
     */
    @Override
    public boolean sendDingWorkMessageCard(String title,String text,String url, List<String> userIds) throws Exception {
        log.info("发送钉钉工作通知:{}",text);
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
        OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
        req.setAgentId(Long.valueOf(dingDingProperties.getAgentId()));
        List<String> strings = userIds.stream().map(this::getDingUserID).collect(Collectors.toList());
        req.setUseridList(String.join(",", strings));
        OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
        obj1.setMsgtype("action_card");
        OapiMessageCorpconversationAsyncsendV2Request.ActionCard obj2 = new OapiMessageCorpconversationAsyncsendV2Request.ActionCard();
        obj2.setTitle(title);
        obj2.setMarkdown(text);
        obj2.setSingleTitle("查看详情");
        obj2.setSingleUrl(url);
        obj1.setActionCard(obj2);
        req.setMsg(obj1);
        OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, getDingToken());
        JSONObject jsonObject = JSONObject.parseObject(rsp.getBody());
        String errmsg = jsonObject.getString("errmsg");
        if(StringUtils.equals(errmsg,"ok")){
            log.info("钉钉消息发送成功!发送内容为:{}-接收人为{}",text,JSONObject.toJSONString(userIds));
            return true;
        }
        return false;
    }

    /**
     * 发送应用机器人消息-文本消息
     * @param text
     * @param userIds
     * @return
     */
    @Override
    public boolean sendRobotTextMessage(String text, List<String> userIds){
        try {
            String robotCode = "ding2wmkojqpxxt0kbwz";
            BatchSendOTOHeaders batchSendOTOHeaders = new BatchSendOTOHeaders();
            batchSendOTOHeaders.setXAcsDingtalkAccessToken(getDingToken());
            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
            batchSendOTORequest.setMsgKey("sampleText");
            batchSendOTORequest.setRobotCode(robotCode);
            List<String> strings = userIds.stream().map(this::getDingUserID).collect(Collectors.toList());
            batchSendOTORequest.setUserIds(strings);

            //batchSendOTORequest.setUserIds(java.util.Arrays.asList("035526016033378597"));
            JSONObject msgParam = new JSONObject();
            msgParam.put("content", text);
            batchSendOTORequest.setMsgParam(msgParam.toJSONString());
            try {
                Config config = new Config();
                config.protocol = "https";
                config.regionId = "central";
                com.aliyun.dingtalkrobot_1_0.Client client = new Client(config);
                BatchSendOTOResponse batchSendOTOResponse = client.batchSendOTOWithOptions(batchSendOTORequest, batchSendOTOHeaders, new RuntimeOptions());
                if (Objects.isNull(batchSendOTOResponse) || Objects.isNull(batchSendOTOResponse.getBody())) {
                    log.error("RobotPrivateMessages_send batchSendOTOResponse return error, response={}",
                            batchSendOTOResponse);
                    return false;
                }
                return true;
            } catch (TeaException e) {
                log.error("RobotPrivateMessages_send batchSendOTOResponse throw TeaException, errCode={}, " +
                        "errorMessage={}", e.getCode(), e.getMessage(), e);
                throw e;
            } catch (Exception e) {
                log.error("RobotPrivateMessages_send batchSendOTOResponse throw Exception", e);
                try {
                    throw e;
                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
            }
        } catch (Exception e) {
            log.error("调用钉钉接口报错!{}", e.getMessage());
        }

        return false;
    }

    /**
     * 发送应用机器人消息-Card
     * @param title
     * @param text
     * @param url
     * @param userIds
     * @return
     */
    @Override
    public boolean sendRobotCardMessage(String title,String text,String url, List<String> userIds){
        try {
            String robotCode = "ding2wmkojqpxxt0kbwz";
            BatchSendOTOHeaders batchSendOTOHeaders = new BatchSendOTOHeaders();
            batchSendOTOHeaders.setXAcsDingtalkAccessToken(getDingToken());
            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
            batchSendOTORequest.setMsgKey("sampleActionCard");
            batchSendOTORequest.setRobotCode(robotCode);
            List<String> strings = userIds.stream().map(this::getDingUserID).collect(Collectors.toList());
            batchSendOTORequest.setUserIds(strings);
            //batchSendOTORequest.setUserIds(java.util.Arrays.asList("035526016033378597"));
            JSONObject msgParam = new JSONObject();
            msgParam.put("title", title);
            msgParam.put("text", text);
            msgParam.put("singleTitle", "查看详情");

            String corpId = dingDingProperties.getCorpId();
            String urlCode = URLEncoder.encode(url);
            String ddUrl = "dingtalk://dingtalkclient/action/openapp?corpid="+corpId+"&container_type=work_platform&app_id=0_"+dingDingProperties.getAgentId()+"&redirect_type=jump&redirect_url="+urlCode;
            msgParam.put("singleURL", ddUrl);
            batchSendOTORequest.setMsgParam(msgParam.toJSONString());
            try {
                Config config = new Config();
                config.protocol = "https";
                config.regionId = "central";
                com.aliyun.dingtalkrobot_1_0.Client client = new Client(config);
                log.error("batchSendOTOWithOptions");
                BatchSendOTOResponse batchSendOTOResponse = client.batchSendOTOWithOptions(batchSendOTORequest, batchSendOTOHeaders, new RuntimeOptions());
                if (Objects.isNull(batchSendOTOResponse) || Objects.isNull(batchSendOTOResponse.getBody())) {
                    log.error("RobotPrivateMessages_send batchSendOTOResponse return error, response={}",
                            batchSendOTOResponse);
                    return false;
                }
                return true;
            } catch (TeaException e) {
                log.error("RobotPrivateMessages_send batchSendOTOResponse throw TeaException, errCode={}, " +
                        "errorMessage={}", e.getCode(), e.getMessage(), e);
                throw e;
            } catch (Exception e) {
                log.error("RobotPrivateMessages_send batchSendOTOResponse throw Exception", e);
                try {
                    throw e;
                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
            }
        } catch (Exception e) {
            log.error("调用钉钉接口报错!{}", e.getMessage());
        }

        return false;
    }


    @Override
    public String uploadFile(String filePath,String acessToken) {
        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/media/upload");
        OapiMediaUploadRequest req = new OapiMediaUploadRequest();
        req.setType("file");
        // 要上传的媒体文件
        FileItem item = new FileItem(filePath);
        req.setMedia(item);
        try {
            OapiMediaUploadResponse rsp = client.execute(req, acessToken);
            JSONObject obj = JSONObject.parseObject(rsp.getBody());
            String mediaId = obj.getString("media_id");
            return mediaId;
        }catch (Exception e) {
            System.out.println(e.getMessage());
        }
        return null;
    }

    /**
     * 发送文件消息
     * @param userIds
     * @param filePath
     * @param fileName
     * @return
     */
    @Override
    public boolean sendRobotFileMessage(List<String> userIds,String filePath,String fileName){
        try {
            String robotCode = "ding2wmkojqpxxt0kbwz";
            String acessToken = getDingToken();
            String mediaId = uploadFile(filePath,acessToken);
            if(StringUtils.isEmpty(mediaId)){
                throw new RuntimeException("文件上传失败");
            }
            //String mediaId = "@lAzPDfYH_E6SO7nOQDVmWM4Npp8i";
            BatchSendOTOHeaders batchSendOTOHeaders = new BatchSendOTOHeaders();
            batchSendOTOHeaders.setXAcsDingtalkAccessToken(acessToken);
            BatchSendOTORequest batchSendOTORequest = new BatchSendOTORequest();
            batchSendOTORequest.setMsgKey("sampleFile");
            batchSendOTORequest.setRobotCode(robotCode);
            List<String> strings = userIds.stream().map(this::getDingUserID).collect(Collectors.toList());
            batchSendOTORequest.setUserIds(strings);

            //batchSendOTORequest.setUserIds(java.util.Arrays.asList("035526016033378597"));
            JSONObject msgParam = new JSONObject();
            msgParam.put("mediaId", mediaId);
            msgParam.put("fileName", fileName);
            msgParam.put("fileType", "xlsx");
            batchSendOTORequest.setMsgParam(msgParam.toJSONString());
            try {
                Config config = new Config();
                config.protocol = "https";
                config.regionId = "central";
                com.aliyun.dingtalkrobot_1_0.Client client = new Client(config);
                BatchSendOTOResponse batchSendOTOResponse = client.batchSendOTOWithOptions(batchSendOTORequest, batchSendOTOHeaders, new RuntimeOptions());
                if (Objects.isNull(batchSendOTOResponse) || Objects.isNull(batchSendOTOResponse.getBody())) {
                    log.error("RobotPrivateMessages_send batchSendOTOResponse return error, response={}",
                            batchSendOTOResponse);
                    return false;
                }
                return true;
            } catch (TeaException e) {
                log.error("RobotPrivateMessages_send batchSendOTOResponse throw TeaException, errCode={}, " +
                        "errorMessage={}", e.getCode(), e.getMessage(), e);
                throw e;
            } catch (Exception e) {
                log.error("RobotPrivateMessages_send batchSendOTOResponse throw Exception", e);
                try {
                    throw e;
                } catch (Exception ex) {
                    throw new RuntimeException(ex);
                }
            }
        } catch (Exception e) {
            log.error("调用钉钉接口报错!{}", e.getMessage());
        }

        return false;
    }

}
