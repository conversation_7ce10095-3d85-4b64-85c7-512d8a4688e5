package com.mh.common.dingding.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 钉钉消息实体
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@Accessors(chain = true)
public class DingDingMessage {

    /**
     * 消息类型：text、markdown、link、actionCard
     */
    private String msgtype;

    /**
     * 文本消息
     */
    private TextMessage text;

    /**
     * Markdown消息
     */
    private MarkdownMessage markdown;

    /**
     * 链接消息
     */
    private LinkMessage link;

    /**
     * ActionCard消息
     */
    private ActionCardMessage actionCard;

    /**
     * @所有人
     */
    private AtMessage at;

    /**
     * 文本消息内容
     */
    @Data
    @Accessors(chain = true)
    public static class TextMessage {
        /**
         * 消息内容
         */
        private String content;
    }

    /**
     * Markdown消息内容
     */
    @Data
    @Accessors(chain = true)
    public static class MarkdownMessage {
        /**
         * 标题
         */
        private String title;
        /**
         * Markdown内容
         */
        private String text;
    }

    /**
     * 链接消息内容
     */
    @Data
    @Accessors(chain = true)
    public static class LinkMessage {
        /**
         * 标题
         */
        private String title;
        /**
         * 消息内容
         */
        private String text;
        /**
         * 图片URL
         */
        private String picUrl;
        /**
         * 点击消息跳转的URL
         */
        private String messageUrl;
    }

    /**
     * ActionCard消息内容
     */
    @Data
    @Accessors(chain = true)
    public static class ActionCardMessage {
        /**
         * 标题
         */
        private String title;
        /**
         * Markdown内容
         */
        private String text;
        /**
         * 按钮标题
         */
        private String singleTitle;
        /**
         * 按钮跳转链接
         */
        private String singleURL;
    }

    /**
     * @消息
     */
    @Data
    @Accessors(chain = true)
    public static class AtMessage {
        /**
         * @指定用户的手机号
         */
        private List<String> atMobiles;
        /**
         * @指定用户的用户ID
         */
        private List<String> atUserIds;
        /**
         * 是否@所有人
         */
        private Boolean isAtAll;
    }

    // ==================== 静态工厂方法 ====================

    /**
     * 创建文本消息
     *
     * @param content 消息内容
     * @return 钉钉消息对象
     */
    public static DingDingMessage createTextMessage(String content) {
        DingDingMessage message = new DingDingMessage();
        message.setMsgtype("text");
        message.setText(new TextMessage().setContent(content));
        message.setAt(new AtMessage().setIsAtAll(false));
        return message;
    }

    /**
     * 创建Markdown消息
     *
     * @param title 标题
     * @param text  Markdown内容
     * @return 钉钉消息对象
     */
    public static DingDingMessage createMarkdownMessage(String title, String text) {
        DingDingMessage message = new DingDingMessage();
        message.setMsgtype("markdown");
        message.setMarkdown(new MarkdownMessage().setTitle(title).setText(text));
        message.setAt(new AtMessage().setIsAtAll(false));
        return message;
    }

    /**
     * 创建链接消息
     *
     * @param title      标题
     * @param text       消息内容
     * @param messageUrl 点击消息跳转的URL
     * @param picUrl     图片URL
     * @return 钉钉消息对象
     */
    public static DingDingMessage createLinkMessage(String title, String text, String messageUrl, String picUrl) {
        DingDingMessage message = new DingDingMessage();
        message.setMsgtype("link");
        message.setLink(new LinkMessage()
                .setTitle(title)
                .setText(text)
                .setMessageUrl(messageUrl)
                .setPicUrl(picUrl));
        return message;
    }

    /**
     * 创建ActionCard消息
     *
     * @param title     标题
     * @param text      Markdown内容
     * @param singleTitle 按钮标题
     * @param singleURL 按钮跳转链接
     * @return 钉钉消息对象
     */
    public static DingDingMessage createActionCardMessage(String title, String text, String singleTitle, String singleURL) {
        DingDingMessage message = new DingDingMessage();
        message.setMsgtype("actionCard");
        message.setActionCard(new ActionCardMessage()
                .setTitle(title)
                .setText(text)
                .setSingleTitle(singleTitle)
                .setSingleURL(singleURL));
        return message;
    }

}
