package com.mh.common.dingding.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.mh.common.core.utils.SpringUtils;
import com.mh.common.dingding.domain.DingDingMessage;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.List;

/**
 * 钉钉工具类
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class DingDingUtils {

    private static final IDingDingService DING_DING_SERVICE = SpringUtils.getBean(IDingDingService.class);

    /**
     * 发送文本消息
     *
     * @param message 消息内容
     * @param userIds 用户ID列表
     * @return 是否发送成功
     */
    public static Boolean sendTextMessage(String message, String... userIds) {
        if (StrUtil.isBlank(message) || userIds == null || userIds.length == 0) {
            log.warn("钉钉消息内容或用户ID为空，跳过发送");
            return false;
        }
        return DING_DING_SERVICE.sendTextMessage(message, Arrays.asList(userIds));
    }

    /**
     * 发送文本消息
     *
     * @param message 消息内容
     * @param userIds 用户ID列表
     * @return 是否发送成功
     */
    public static Boolean sendTextMessage(String message, List<String> userIds) {
        if (StrUtil.isBlank(message) || CollUtil.isEmpty(userIds)) {
            log.warn("钉钉消息内容或用户ID为空，跳过发送");
            return false;
        }
        return DING_DING_SERVICE.sendTextMessage(message, userIds);
    }

    /**
     * 发送Markdown消息
     *
     * @param title   标题
     * @param content Markdown内容
     * @param userIds 用户ID列表
     * @return 是否发送成功
     */
    public static Boolean sendMarkdownMessage(String title, String content, String... userIds) {
        if (StrUtil.isBlank(title) || StrUtil.isBlank(content) || userIds == null || userIds.length == 0) {
            log.warn("钉钉消息标题、内容或用户ID为空，跳过发送");
            return false;
        }
        return DING_DING_SERVICE.sendMarkdownMessage(title, content, Arrays.asList(userIds));
    }

    /**
     * 发送Markdown消息
     *
     * @param title   标题
     * @param content Markdown内容
     * @param userIds 用户ID列表
     * @return 是否发送成功
     */
    public static Boolean sendMarkdownMessage(String title, String content, List<String> userIds) {
        if (StrUtil.isBlank(title) || StrUtil.isBlank(content) || CollUtil.isEmpty(userIds)) {
            log.warn("钉钉消息标题、内容或用户ID为空，跳过发送");
            return false;
        }
        return DING_DING_SERVICE.sendMarkdownMessage(title, content, userIds);
    }

    /**
     * 发送机器人文本消息
     *
     * @param content 消息内容
     * @return 是否发送成功
     */
    public static Boolean sendRobotTextMessage(String content) {
        if (StrUtil.isBlank(content)) {
            log.warn("钉钉机器人消息内容为空，跳过发送");
            return false;
        }
        DingDingMessage message = DingDingMessage.createTextMessage(content);
        return DING_DING_SERVICE.sendRobotMessage(message);
    }

    /**
     * 发送机器人文本消息@所有人
     *
     * @param content 消息内容
     * @return 是否发送成功
     */
    public static Boolean sendRobotTextMessageAtAll(String content) {
        if (StrUtil.isBlank(content)) {
            log.warn("钉钉机器人消息内容为空，跳过发送");
            return false;
        }
        DingDingMessage message = DingDingMessage.createTextMessage(content);
        message.getAt().setIsAtAll(true);
        return DING_DING_SERVICE.sendRobotMessage(message);
    }

    /**
     * 发送机器人Markdown消息
     *
     * @param title   标题
     * @param content Markdown内容
     * @return 是否发送成功
     */
    public static Boolean sendRobotMarkdownMessage(String title, String content) {
        if (StrUtil.isBlank(title) || StrUtil.isBlank(content)) {
            log.warn("钉钉机器人消息标题或内容为空，跳过发送");
            return false;
        }
        DingDingMessage message = DingDingMessage.createMarkdownMessage(title, content);
        return DING_DING_SERVICE.sendRobotMessage(message);
    }

    /**
     * 发送机器人链接消息
     *
     * @param title      标题
     * @param text       消息内容
     * @param messageUrl 点击消息跳转的URL
     * @param picUrl     图片URL
     * @return 是否发送成功
     */
    public static Boolean sendRobotLinkMessage(String title, String text, String messageUrl, String picUrl) {
        if (StrUtil.isBlank(title) || StrUtil.isBlank(text) || StrUtil.isBlank(messageUrl)) {
            log.warn("钉钉机器人链接消息参数不完整，跳过发送");
            return false;
        }
        DingDingMessage message = DingDingMessage.createLinkMessage(title, text, messageUrl, picUrl);
        return DING_DING_SERVICE.sendRobotMessage(message);
    }

    /**
     * 通过手机号获取用户ID
     *
     * @param mobile 手机号
     * @return 用户ID
     */
    public static String getUserIdByMobile(String mobile) {
        if (StrUtil.isBlank(mobile)) {
            log.warn("手机号为空，无法获取用户ID");
            return null;
        }
        return DING_DING_SERVICE.getUserIdByMobile(mobile);
    }
}
