package com.mh.common.dingding.config;

import com.mh.common.dingding.service.DingdingNoticeService;
import com.mh.common.dingding.service.impl.DingdingNoticeServiceImpl;
import com.mh.common.redis.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 钉钉自动配置类
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Slf4j
@AutoConfiguration
@EnableConfigurationProperties(DingDingProperties.class)
@ConditionalOnProperty(prefix = "dingding", name = "enabled", havingValue = "true")
public class DingDingAutoConfiguration {

    public DingDingAutoConfiguration() {
        log.info("[钉钉模块] 自动配置已启用");
    }

    @Bean
    @ConditionalOnMissingBean
    public DingdingNoticeService dingdingNoticeService(DingDingProperties dingDingProperties) {
        log.info("[钉钉模块] 注册钉钉服务实现类");
        return new DingdingNoticeServiceImpl(dingDingProperties);
    }
}
