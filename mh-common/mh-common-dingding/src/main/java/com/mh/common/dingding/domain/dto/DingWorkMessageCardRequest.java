package com.mh.common.dingding.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 钉钉工作消息卡片请求
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@Accessors(chain = true)
public class DingWorkMessageCardRequest {

    /**
     * 消息标题
     */
    @NotBlank(message = "消息标题不能为空")
    private String title;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String text;

    /**
     * 用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空")
    private List<String> userIds;

    /**
     * 按钮标题
     */
    private String singleTitle;

    /**
     * 按钮跳转链接
     */
    private String singleURL;

    /**
     * 是否@所有人
     */
    private Boolean atAll = false;

    /**
     * @指定用户的手机号列表
     */
    private List<String> atMobiles;
}
