package com.mh.common.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 钉钉工作卡片消息请求DTO
 *
 * <AUTHOR>
 * @date 2021/12/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("钉钉工作卡片消息请求")
public class DingWorkMessageCardRequest {

    @ApiModelProperty(value = "卡片标题", required = true)
    @NotBlank(message = "卡片标题不能为空")
    private String title;

    @ApiModelProperty(value = "卡片内容", required = true)
    @NotBlank(message = "卡片内容不能为空")
    private String text;

    @ApiModelProperty(value = "跳转链接", required = true)
    @NotBlank(message = "跳转链接不能为空")
    private String url;

    @ApiModelProperty(value = "用户手机号列表", required = true)
    @NotEmpty(message = "用户手机号列表不能为空")
    private List<String> userIds;
}
