package com.mh.common.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 钉钉机器人文件消息请求DTO
 *
 * <AUTHOR>
 * @date 2021/12/24
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("钉钉机器人文件消息请求")
public class DingRobotFileMessageRequest {

    @ApiModelProperty(value = "用户手机号列表", required = true)
    @NotEmpty(message = "用户手机号列表不能为空")
    private List<String> userIds;

    @ApiModelProperty(value = "文件路径", required = true)
    @NotBlank(message = "文件路径不能为空")
    private String filePath;

    @ApiModelProperty(value = "文件名", required = true)
    @NotBlank(message = "文件名不能为空")
    private String fileName;
}
