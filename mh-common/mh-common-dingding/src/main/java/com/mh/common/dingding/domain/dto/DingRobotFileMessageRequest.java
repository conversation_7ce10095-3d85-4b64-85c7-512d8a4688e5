package com.mh.common.dingding.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;

/**
 * 钉钉机器人文件消息请求
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@Accessors(chain = true)
public class DingRobotFileMessageRequest {

    /**
     * 文件媒体ID
     */
    @NotBlank(message = "文件媒体ID不能为空")
    private String mediaId;

    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 机器人代码
     */
    private String robotCode;

    /**
     * 消息键
     */
    private String msgKey = "sampleFile";
}
