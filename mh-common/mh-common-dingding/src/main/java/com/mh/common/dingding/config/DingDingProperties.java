package com.mh.common.dingding.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 钉钉配置属性
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@Component
@ConfigurationProperties(prefix = "dingding")
public class DingDingProperties {

    /**
     * 是否启用钉钉功能
     */
    private Boolean enabled = false;

    /**
     * 应用Key (AppKey)
     */
    private String appKey;

    /**
     * 应用Secret (AppSecret)
     */
    private String appSecret;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 代理ID (AgentId)
     */
    private String agentId;

    /**
     * 重定向URI
     */
    private String redirectUri;

    /**
     * 重定向认证URL
     */
    private String redirectAuthUrl;

    /**
     * 授权范围
     */
    private String scope = "openid";

    /**
     * 响应类型
     */
    private String responseType = "code";

    /**
     * 授权类型
     */
    private String grantType = "authorization_code";

    /**
     * 提示类型
     */
    private String prompt = "consent";

    /**
     * 机器人Webhook地址
     */
    private String robotWebhook;

    /**
     * 机器人密钥
     */
    private String robotSecret;

    /**
     * API基础URL
     */
    private String apiBaseUrl = "https://oapi.dingtalk.com";

    /**
     * 连接超时时间（毫秒）
     */
    private Integer connectTimeout = 5000;

    /**
     * 读取超时时间（毫秒）
     */
    private Integer readTimeout = 10000;

}
