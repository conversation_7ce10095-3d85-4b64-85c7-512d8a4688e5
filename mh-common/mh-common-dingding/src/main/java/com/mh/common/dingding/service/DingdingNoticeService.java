package com.mh.common.dingding.service;

import com.alibaba.fastjson.JSONArray;

import java.util.List;

/**
 * 丁鼎通知服务
 *
 * <AUTHOR>
 * @date 2021/12/24
 */
public interface DingdingNoticeService {

   /**
    * 得到丁令牌
    *
    * @return {@link String}
    * @throws Exception 异常
    */
   String getDingToken() throws Exception;

   /**
    * 得到丁用户id
    *
    * @return {@link String}
    */
   String getDingUserID(String mobile);

   //获取部门ID列表
   public List<Long> getDeptIdList(Long deptId);

   //获取部门列表
   public JSONArray getDeptList(Long deptId);

   public JSONArray getUserList(Long deptId);


   /**
    * 发出叮的工作信息
    *  @param text    文本
    * @param userIds 用户id
    * @return
    */
   boolean sendDingWorkMessage(String text, List<String> userIds) throws Exception;

   /**
    * 发出卡片消息action_card，带链接的
    * @param title
    * @param text
    * @param url
    * @param userIds
    * @return
    * @throws Exception
    */
   boolean sendDingWorkMessageCard(String title,String text,String url, List<String> userIds) throws Exception;

   /**
    * 发送应用消息-action_card
    * @param title
    * @param text
    * @param url
    * @param userIds
    * @return
    */
   public boolean sendRobotCardMessage(String title,String text,String url, List<String> userIds);

   /**
    * 发送应用消息-文本类型
    * @param text
    * @param userIds
    * @return
    */
   public boolean sendRobotTextMessage(String text, List<String> userIds);

   /**
    * excel文件推送
    * @param userIds
    * @param filePath
    * @param fileName
    * @return
    */
   public boolean sendRobotFileMessage(List<String> userIds,String filePath,String fileName);


   /**
    * 上传文件
    * @param filePath
    * @param acessToken
    * @return
    */
   public String uploadFile(String filePath,String acessToken);
}
