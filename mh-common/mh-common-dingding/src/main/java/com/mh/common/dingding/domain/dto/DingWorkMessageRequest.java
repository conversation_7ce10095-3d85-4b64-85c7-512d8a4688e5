package com.mh.common.dingding.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 钉钉工作消息请求
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Data
@Accessors(chain = true)
public class DingWorkMessageRequest {

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String text;

    /**
     * 用户ID列表
     */
    @NotEmpty(message = "用户ID列表不能为空")
    private List<String> userIds;

    /**
     * 消息类型：text、markdown、link、actionCard
     */
    private String msgType = "text";

    /**
     * 是否@所有人
     */
    private Boolean atAll = false;

    /**
     * @指定用户的手机号列表
     */
    private List<String> atMobiles;
}
