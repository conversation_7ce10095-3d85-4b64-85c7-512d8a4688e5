<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mh</groupId>
    <artifactId>mh-common-bom</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>

    <description>
        mh-common-bom common依赖项
    </description>

    <properties>
        <revision>5.4.1</revision>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- 核心模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-core</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 接口模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-doc</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- excel -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-excel</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 幂等 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-idempotent</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 调度模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 日志记录 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-log</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 邮件服务 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-mail</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库服务 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-mybatis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- OSS -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-oss</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 限流 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-ratelimiter</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 缓存服务 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-redis</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- satoken -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-satoken</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 安全模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-security</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 短信模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-sms</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-social</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- web服务 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-web</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 翻译模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-translation</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 脱敏模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-sensitive</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 序列化模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-json</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 数据库加解密模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-encrypt</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 租户模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-tenant</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- WebSocket模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-websocket</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- SSE模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-sse</artifactId>
                <version>${revision}</version>
            </dependency>

            <!-- 钉钉模块 -->
            <dependency>
                <groupId>com.mh</groupId>
                <artifactId>mh-common-dingding</artifactId>
                <version>${revision}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

</project>
